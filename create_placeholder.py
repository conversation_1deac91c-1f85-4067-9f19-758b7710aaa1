from PIL import Image, ImageDraw, ImageFont
import os

def create_placeholder_image():
    # إنشاء صورة بديلة
    width, height = 300, 200
    image = Image.new('RGB', (width, height), color='#f8f9fa')
    draw = ImageDraw.Draw(image)
    
    # رسم إطار
    draw.rectangle([10, 10, width-10, height-10], outline='#dee2e6', width=3)
    
    # إضافة نص
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    text = "🖼️ Image"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    draw.text((x, y), text, fill='#6c757d', font=font)
    
    # حفظ الصورة
    output_path = 'images/placeholder.jpg'
    image.save(output_path, 'JPEG', quality=95)
    print(f"تم إنشاء الصورة البديلة: {output_path}")

if __name__ == "__main__":
    create_placeholder_image()
