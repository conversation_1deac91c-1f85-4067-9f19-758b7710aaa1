
# 🍍 دليل الأصول للعبة جزيرة الأناناس

مهلاً! هذا هو دليلك الكامل لجميع ملفات الصور والأصوات التي تحتاج إلى إنشائها أو إضافتها إلى اللعبة. سيساعدك هذا الدليل على استبدال جميع العناصر النائبة بأصول حقيقية لجعل اللعبة أكثر جاذبية.

## 🖼️ الصور المطلوبة (Images)

يتم حاليًا استخدام صور نائبة (`images/placeholder.svg`) للعديد من عناصر اللعبة. يجب إنشاء الصور التالية ووضعها في مجلد `images`. أقترح استخدام تنسيق `.png` أو `.jpg` للصور.

### 🔡 لعبة الحروف (Alphabet Game)

تحتاج كل كلمة إلى صورة مرتبطة بها.

*   `apple.png` (للتفاحة)
*   `ball.png` (للكرة)
*   `cat.png` (للقطة)
*   `dog.png` (للكلب)
*   `elephant.png` (للفيل)
*   `fish.png` (للسمكة)
*   `giraffe.png` (للزرافة)
*   `house.png` (للمنزل)

### 👨‍👩‍👧‍👦 لعبة العائلة (Family Game)

صور لأفراد العائلة.

*   `father.png` (للأب)
*   `mother.png` (للأم)
*   `brother.png` (للأخ)
*   `sister.png` (للأخت)
*   `grandfather.png` (للجد)
*   `grandmother.png` (للجدة)

### 👤 لعبة أجزاء الجسم (Body Parts Game)

صور لأجزاء الجسم.

*   `head.png` (للرأس)
*   `eye.png` (للعين)
*   `ear.png` (للأذن)
*   `nose.png` (للأنف)
*   `mouth.png` (للفم)
*   `hand.png` (لليد)
*   `foot.png` (للقدم)

### 🐾 لعبة الحيوانات (Animals Game)

صور للحيوانات.

*   `cat.png` (للقطة)
*   `dog.png` (للكلب)
*   `bird.png` (للطائر)
*   `elephant.png` (للفيل)
*   `monkey.png` (للقرد)
*   `lion.png` (للأسد)

### 🍎 لعبة الطعام (Food Game)

صور للأطعمة والمشروبات.

*   `apple.png` (للتفاحة)
*   `banana.png` (للموزة)
*   `cake.png` (للكعكة)
*   `water.png` (للماء)
*   `milk.png` (للحليب)
*   `bread.png` (للخبز)

---

## 🔊 الأصوات المطلوبة (Sounds)

يجب وضع جميع الملفات الصوتية في مجلد `sounds`. يُفضل استخدام تنسيق `.mp3`.

### 🎮 أصوات واجهة المستخدم وتأثيرات اللعبة (UI & Game Effects)

هذه الأصوات تُستخدم للتفاعل مع اللعبة والاحتفالات.

*   `correct.mp3`: صوت قصير ومبهج عند الإجابة الصحيحة.
*   `wrong.mp3`: صوت للإشارة إلى إجابة خاطئة.
*   `excellent.mp3`: صوت احتفالي لإنجاز ممتاز.
*   `try_again.mp3`: صوت تشجيعي للمحاولة مرة أخرى.
*   `wonderful.mp3`: صوت إيجابي آخر للإجابة الصحيحة.
*   `great.mp3`: صوت رائع آخر للإجابة الصحيحة.
*   `applause.mp3`: صوت تصفيق عند إكمال المستوى بنجاح.

### 🗣️ أصوات المفردات (Vocabulary Sounds)

يتم إنشاء أسماء هذه الملفات ديناميكيًا في الكود. النمط هو `الكلمة_اللغة.mp3`.

#### المستوى الأول (Level 1)

**لعبة الحروف (Alphabet Game):**
*   `apple_en.mp3`, `apple_ar.mp3`
*   `ball_en.mp3`, `ball_ar.mp3`
*   `cat_en.mp3`, `cat_ar.mp3`
*   `dog_en.mp3`, `dog_ar.mp3`
*   `elephant_en.mp3`, `elephant_ar.mp3`
*   `fish_en.mp3`, `fish_ar.mp3`
*   `giraffe_en.mp3`, `giraffe_ar.mp3`
*   `house_en.mp3`, `house_ar.mp3`

**لعبة الألوان (Colors Game):**
*   `red_en.mp3`, `red_ar.mp3`
*   `blue_en.mp3`, `blue_ar.mp3`
*   `yellow_en.mp3`, `yellow_ar.mp3`
*   `green_en.mp3`, `green_ar.mp3`
*   `orange_en.mp3`, `orange_ar.mp3`
*   `purple_en.mp3`, `purple_ar.mp3`
*   `black_en.mp3`, `black_ar.mp3`
*   `white_en.mp3`, `white_ar.mp3`

**لعبة الأرقام (Numbers Game):**
*   `1_en.mp3`, `1.mp3` (or `1_ar.mp3`)
*   `2_en.mp3`, `2.mp3`
*   ... وهكذا حتى 10.

**لعبة العائلة (Family Game):**
*   `father_en.mp3`, `father_ar.mp3`
*   `mother_en.mp3`, `mother_ar.mp3`
*   `brother_en.mp3`, `brother_ar.mp3`
*   `sister_en.mp3`, `sister_ar.mp3`
*   `grandfather_en.mp3`, `grandfather_ar.mp3`
*   `grandmother_en.mp3`, `grandmother_ar.mp3`

**لعبة أجزاء الجسم (Body Parts Game):**
*   `head_en.mp3`, `head_ar.mp3`
*   `eye_en.mp3`, `eye_ar.mp3`
*   `ear_en.mp3`, `ear_ar.mp3`
*   `nose_en.mp3`, `nose_ar.mp3`
*   `mouth_en.mp3`, `mouth_ar.mp3`
*   `hand_en.mp3`, `hand_ar.mp3`
*   `foot_en.mp3`, `foot_ar.mp3`

**لعبة الطعام (Food Game):**
*   `apple_en.mp3`, `apple_ar.mp3`
*   `banana_en.mp3`, `banana_ar.mp3`
*   `cake_en.mp3`, `cake_ar.mp3`
*   `water_en.mp3`, `water_ar.mp3`
*   `milk_en.mp3`, `milk_ar.mp3`
*   `bread_en.mp3`, `bread_ar.mp3`

### 🦁 أصوات الحيوانات (Animal Sounds)

على الرغم من أن الكود لا يستدعيها حاليًا، إلا أنه من الجيد إضافتها لتحسين اللعبة.

*   `cat_sound.mp3` (صوت مواء)
*   `dog_sound.mp3` (صوت نباح)
*   `bird_sound.mp3` (صوت زقزقة)
*   `elephant_sound.mp3` (صوت الفيل)
*   `monkey_sound.mp3` (صوت القرد)
*   `lion_sound.mp3` (صوت زئير)

---
آمل أن يكون هذا الدليل مفيدًا!
