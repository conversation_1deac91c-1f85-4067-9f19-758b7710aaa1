// وظائف الألعاب العامة والمساعدة

// وظيفة لإنشاء صور placeholder
function createPlaceholderImage(text, width = 300, height = 200) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // خلفية ملونة
    const colors = ['#FF6B35', '#F7931E', '#FFD23F', '#4CAF50', '#2196F3', '#9C27B0'];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    ctx.fillStyle = randomColor;
    ctx.fillRect(0, 0, width, height);
    
    // نص
    ctx.fillStyle = 'white';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, width / 2, height / 2);
    
    return canvas.toDataURL();
}

// وظيفة لإنشاء أيقونة emoji كصورة
function createEmojiImage(emoji, size = 200) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // خلفية دائرية
    ctx.fillStyle = '#f8f9fa';
    ctx.beginPath();
    ctx.arc(size / 2, size / 2, size / 2 - 10, 0, 2 * Math.PI);
    ctx.fill();
    
    // إيموجي
    ctx.font = `${size * 0.6}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(emoji, size / 2, size / 2);
    
    return canvas.toDataURL();
}

// وظيفة لإنشاء صورة لون
function createColorImage(color, size = 200) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // مربع ملون
    ctx.fillStyle = color;
    ctx.fillRect(20, 20, size - 40, size - 40);
    
    // إطار
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 4;
    ctx.strokeRect(20, 20, size - 40, size - 40);
    
    return canvas.toDataURL();
}

// وظيفة لإنشاء صورة رقم
function createNumberImage(number, size = 200) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // خلفية
    ctx.fillStyle = '#e3f2fd';
    ctx.fillRect(0, 0, size, size);
    
    // رقم
    ctx.fillStyle = '#1976d2';
    ctx.font = `bold ${size * 0.6}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(number.toString(), size / 2, size / 2);
    
    return canvas.toDataURL();
}

// وظيفة لمعالجة الصور المفقودة
function handleImageError(img, fallbackText) {
    if (img.src.includes('placeholder.jpg')) return;
    
    // إنشاء صورة بديلة
    img.src = createPlaceholderImage(fallbackText);
}

// نظام الأصوات المحسن
function playAudio(audioFile, fallbackText = '', lang = 'en') {
    if (!gameSettings.soundEnabled) return;

    // محاولة تشغيل الملف الصوتي أولاً
    const audio = new Audio(`sounds/${audioFile}`);

    audio.onloadeddata = function() {
        audio.play().catch(error => {
            console.log('Audio file not found, using speech synthesis:', error);
            fallbackToSpeech(fallbackText, lang);
        });
    };

    audio.onerror = function() {
        console.log('Audio file not found, using speech synthesis');
        fallbackToSpeech(fallbackText, lang);
    };

    // تحميل الملف الصوتي
    audio.load();
}

function fallbackToSpeech(text, lang = 'en') {
    if (!text || !('speechSynthesis' in window)) return;

    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = lang === 'ar' ? 'ar-SA' : 'en-US';
    utterance.rate = 0.8;
    utterance.pitch = 1.2;
    utterance.volume = 1;

    speechSynthesis.speak(utterance);
}

// وظيفة محسنة للنطق مع دعم الملفات الصوتية
function enhancedSpeak(text, lang = 'ar', audioFile = null) {
    if (!gameSettings.soundEnabled) return;

    // تأثيرات بصرية
    const speakButtons = document.querySelectorAll('.speak-btn');
    speakButtons.forEach(btn => {
        if (btn.textContent.includes(lang === 'ar' ? 'العربية' : 'English')) {
            btn.classList.add('speaking');
        }
    });

    const resetButtons = () => {
        speakButtons.forEach(btn => {
            btn.classList.remove('speaking');
        });
    };

    if (audioFile) {
        playAudio(audioFile, text, lang);
        setTimeout(resetButtons, 2000);
    } else {
        fallbackToSpeech(text, lang);
        setTimeout(resetButtons, 2000);
    }
}

// وظيفة لإضافة تأثيرات بصرية للإجابات
function animateAnswer(button, isCorrect) {
    button.disabled = true;
    
    if (isCorrect) {
        button.classList.add('correct-answer');
        // إضافة أيقونة صح
        const checkIcon = document.createElement('span');
        checkIcon.innerHTML = ' ✓';
        checkIcon.style.fontSize = '1.5em';
        button.appendChild(checkIcon);
    } else {
        button.classList.add('wrong-answer');
        // إضافة أيقونة خطأ
        const crossIcon = document.createElement('span');
        crossIcon.innerHTML = ' ✗';
        crossIcon.style.fontSize = '1.5em';
        button.appendChild(crossIcon);
    }
}

// وظيفة لإظهار الإجابة الصحيحة
function highlightCorrectAnswer(buttons, correctAnswer, selectedAnswer) {
    buttons.forEach(btn => {
        btn.disabled = true;
        
        const btnText = btn.textContent.trim();
        
        if (btnText.includes(correctAnswer) || btnText === correctAnswer) {
            btn.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            btn.style.color = 'white';
            btn.style.borderColor = '#4CAF50';
            
            // إضافة أيقونة صح
            if (!btn.querySelector('.check-icon')) {
                const checkIcon = document.createElement('span');
                checkIcon.className = 'check-icon';
                checkIcon.innerHTML = ' ✓';
                checkIcon.style.fontSize = '1.5em';
                btn.appendChild(checkIcon);
            }
        } else if (btnText.includes(selectedAnswer) || btnText === selectedAnswer) {
            btn.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
            btn.style.color = 'white';
            btn.style.borderColor = '#f44336';
            
            // إضافة أيقونة خطأ
            if (!btn.querySelector('.cross-icon')) {
                const crossIcon = document.createElement('span');
                crossIcon.className = 'cross-icon';
                crossIcon.innerHTML = ' ✗';
                crossIcon.style.fontSize = '1.5em';
                btn.appendChild(crossIcon);
            }
        } else {
            btn.style.opacity = '0.6';
        }
    });
}

// وظيفة لإنشاء تأثير الاحتفال المحسن
function celebrateCorrectAnswer() {
    // تشغيل صوت الاحتفال
    playAudio('correct.mp3', 'ممتاز!', 'ar');

    // إنشاء كونفيتي وبالونات
    const colors = ['#ff6b35', '#f7931e', '#ffd23f', '#4CAF50', '#2196F3', '#9c27b0'];
    const shapes = ['🎈', '🎉', '⭐', '🌟', '✨', '🎊'];

    // كونفيتي متساقط
    for (let i = 0; i < 30; i++) {
        setTimeout(() => {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.width = Math.random() * 15 + 10 + 'px';
            confetti.style.height = confetti.style.width;
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.borderRadius = Math.random() > 0.5 ? '50%' : '0';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '9999';
            confetti.style.animation = `fall ${Math.random() * 2 + 3}s linear forwards`;
            confetti.style.transform = `rotate(${Math.random() * 360}deg)`;

            document.body.appendChild(confetti);

            setTimeout(() => {
                confetti.remove();
            }, 5000);
        }, i * 50);
    }

    // بالونات وأشكال emoji
    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            const balloon = document.createElement('div');
            balloon.textContent = shapes[Math.floor(Math.random() * shapes.length)];
            balloon.style.position = 'fixed';
            balloon.style.left = Math.random() * 90 + 5 + 'vw';
            balloon.style.top = '100vh';
            balloon.style.fontSize = Math.random() * 20 + 30 + 'px';
            balloon.style.pointerEvents = 'none';
            balloon.style.zIndex = '9999';
            balloon.style.animation = `floatUp ${Math.random() * 2 + 4}s ease-out forwards`;

            document.body.appendChild(balloon);

            setTimeout(() => {
                balloon.remove();
            }, 6000);
        }, i * 100);
    }
}

// احتفال كبير للنتائج النهائية
function celebrateGameCompletion() {
    // تشغيل صوت التصفيق
    playAudio('applause.mp3', 'تصفيق حار!', 'ar');

    // احتفال أكبر
    const colors = ['#ff6b35', '#f7931e', '#ffd23f', '#4CAF50', '#2196F3', '#9c27b0'];
    const celebrationEmojis = ['🎉', '🎊', '🥳', '🎈', '🌟', '⭐', '✨', '🏆', '👏'];

    // موجة من الكونفيتي
    for (let wave = 0; wave < 3; wave++) {
        setTimeout(() => {
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.position = 'fixed';
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.top = '-20px';
                    confetti.style.width = Math.random() * 20 + 8 + 'px';
                    confetti.style.height = confetti.style.width;
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.borderRadius = Math.random() > 0.3 ? '50%' : '0';
                    confetti.style.pointerEvents = 'none';
                    confetti.style.zIndex = '9999';
                    confetti.style.animation = `fall ${Math.random() * 3 + 4}s linear forwards`;
                    confetti.style.transform = `rotate(${Math.random() * 360}deg)`;

                    document.body.appendChild(confetti);

                    setTimeout(() => {
                        confetti.remove();
                    }, 7000);
                }, i * 20);
            }
        }, wave * 1000);
    }

    // إيموجي احتفالية
    for (let i = 0; i < 25; i++) {
        setTimeout(() => {
            const emoji = document.createElement('div');
            emoji.textContent = celebrationEmojis[Math.floor(Math.random() * celebrationEmojis.length)];
            emoji.style.position = 'fixed';
            emoji.style.left = Math.random() * 90 + 5 + 'vw';
            emoji.style.top = '100vh';
            emoji.style.fontSize = Math.random() * 30 + 40 + 'px';
            emoji.style.pointerEvents = 'none';
            emoji.style.zIndex = '9999';
            emoji.style.animation = `floatUp ${Math.random() * 3 + 5}s ease-out forwards`;
            emoji.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';

            document.body.appendChild(emoji);

            setTimeout(() => {
                emoji.remove();
            }, 8000);
        }, i * 150);
    }
}

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes fall {
        0% {
            transform: translateY(-20px) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(720deg);
            opacity: 0;
        }
    }

    @keyframes floatUp {
        0% {
            transform: translateY(0) scale(0.5);
            opacity: 0.8;
        }
        50% {
            opacity: 1;
            transform: scale(1.2);
        }
        100% {
            transform: translateY(-100vh) scale(0.8);
            opacity: 0;
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    @keyframes glow {
        0%, 100% {
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
        }
        50% {
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.8);
        }
    }
`;
document.head.appendChild(style);

// وظيفة لحفظ التقدم محلياً
function saveProgress() {
    const progress = {
        currentLevel: currentLevel,
        totalScore: totalScore,
        completedLevels: JSON.parse(localStorage.getItem('completedLevels') || '[]'),
        lastPlayed: new Date().toISOString()
    };
    
    localStorage.setItem('gameProgress', JSON.stringify(progress));
}

// وظيفة لتحميل التقدم المحفوظ
function loadProgress() {
    const saved = localStorage.getItem('gameProgress');
    if (saved) {
        const progress = JSON.parse(saved);
        return progress;
    }
    return null;
}

// وظيفة لتسجيل إكمال مستوى
function markLevelCompleted(level) {
    let completedLevels = JSON.parse(localStorage.getItem('completedLevels') || '[]');
    if (!completedLevels.includes(level)) {
        completedLevels.push(level);
        localStorage.setItem('completedLevels', JSON.stringify(completedLevels));
    }
}

// وظيفة لإنشاء تقرير مفصل للأداء
function generatePerformanceReport() {
    const report = {
        level: currentLevel,
        totalQuestions: totalQuestions,
        correctAnswers: correctAnswers,
        wrongAnswers: totalQuestions - correctAnswers,
        accuracy: totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0,
        score: totalScore,
        timeSpent: Date.now() - (window.gameStartTime || Date.now()),
        date: new Date().toISOString()
    };
    
    // حفظ التقرير
    let reports = JSON.parse(localStorage.getItem('performanceReports') || '[]');
    reports.push(report);
    
    // الاحتفاظ بآخر 50 تقرير فقط
    if (reports.length > 50) {
        reports = reports.slice(-50);
    }
    
    localStorage.setItem('performanceReports', JSON.stringify(reports));
    
    return report;
}

// وظيفة لإنشاء رسائل تشجيعية مخصصة
function getEncouragementMessage(accuracy) {
    const messages = {
        excellent: [
            "أنت نجم حقيقي! ⭐",
            "أداء رائع ومتميز! 🌟",
            "مبدع ومتفوق! 🏆",
            "استمر في التألق! ✨"
        ],
        good: [
            "أداء جيد جداً! 👏",
            "تحسن ملحوظ! 📈",
            "على الطريق الصحيح! 🎯",
            "استمر في المحاولة! 💪"
        ],
        fair: [
            "بداية جيدة! 🌱",
            "يمكنك تحسين أكثر! 📚",
            "استمر في التعلم! 🎓",
            "لا تستسلم! 🚀"
        ],
        needsWork: [
            "حاول مرة أخرى! 🔄",
            "التعلم يحتاج صبر! ⏰",
            "كل محاولة تقربك للنجاح! 🎯",
            "أنت قادر على الأفضل! 💫"
        ]
    };
    
    let category;
    if (accuracy >= 90) category = 'excellent';
    else if (accuracy >= 70) category = 'good';
    else if (accuracy >= 50) category = 'fair';
    else category = 'needsWork';
    
    const categoryMessages = messages[category];
    return categoryMessages[Math.floor(Math.random() * categoryMessages.length)];
}

// تهيئة متغيرات الوقت
window.gameStartTime = Date.now();
