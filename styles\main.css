/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, Arial, sans-serif;
    background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd23f, #4CAF50, #2196F3, #9c27b0);
    background-size: 400% 400%;
    animation: gradientShift 12s ease infinite;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    direction: rtl;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
    100% { background-position: 0% 50%; }
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 10px;
    position: relative;
    z-index: 1;
}

/* الشاشات */
.screen {
    display: none;
    min-height: 100vh;
    padding: 20px;
    animation: fadeIn 0.5s ease-in-out;
}

.screen.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* شاشة الترحيب */
.welcome-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 40px 20px;
    margin: 50px auto;
    max-width: 800px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.main-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: #ff6b35;
    margin-bottom: 10px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.2);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.subtitle {
    font-size: clamp(1.2rem, 3vw, 2rem);
    color: #333;
    margin-bottom: 20px;
}

.description {
    font-size: clamp(1rem, 2vw, 1.3rem);
    color: #666;
    line-height: 1.6;
    margin-bottom: 30px;
}

/* العناصر العائمة */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(5deg); }
    50% { transform: translateY(-30px) rotate(-5deg); }
    75% { transform: translateY(-15px) rotate(3deg); }
}

/* الأزرار */
.start-btn, .back-btn, .play-again-btn, .next-level-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 15px 40px;
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    font-weight: bold;
    cursor: pointer;
    margin: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    text-transform: none;
}

.start-btn:hover, .play-again-btn:hover, .next-level-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
}

.back-btn {
    background: linear-gradient(45deg, #ff6b35, #e55a2b);
}

.back-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
}

/* شاشة اختيار المستوى */
.level-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 30px 20px;
    margin: 20px auto;
    max-width: 1200px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    color: #333;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.level-description {
    font-size: clamp(1rem, 2vw, 1.3rem);
    color: #666;
    margin-bottom: 30px;
}

.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.level-card {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 20px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.level-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd23f, #4CAF50, #2196F3, #9c27b0);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.level-card:hover::before {
    opacity: 0.1;
}

.level-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: #4CAF50;
}

.level-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.level-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 8px;
}

.level-card p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 15px;
}

.level-topics {
    font-size: 0.9rem;
    color: #888;
    line-height: 1.4;
    background: rgba(76, 175, 80, 0.1);
    padding: 10px;
    border-radius: 10px;
}

/* شاشة اللعبة */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    gap: 10px;
}

.level-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.level-info span {
    font-weight: bold;
    color: #333;
}

#current-level {
    font-size: 1.2rem;
    color: #4CAF50;
}

#current-game {
    font-size: 1rem;
    color: #666;
}

.progress-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    margin: 0 20px;
}

.progress-container {
    width: 100%;
    max-width: 300px;
    height: 12px;
    background: #f1f1f1;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 6px;
}

#score-display {
    font-weight: bold;
    color: #333;
    font-size: 1rem;
}

.home-btn {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.home-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.game-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    min-height: 500px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* شاشة النتائج */
.results-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 40px 20px;
    margin: 50px auto;
    max-width: 600px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

#results-title {
    font-size: clamp(2rem, 4vw, 3rem);
    color: #4CAF50;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.results-stars {
    font-size: 3rem;
    margin: 20px 0;
    animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.results-details {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    font-size: 1.1rem;
}

.score-item:last-child {
    border-bottom: none;
}

.encouragement-message {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 15px;
    border-radius: 15px;
    margin: 20px 0;
    font-size: 1.2rem;
    font-weight: bold;
}

.results-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

/* أنماط الألعاب */
.question-container {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px solid #e9ecef;
}

.question-image {
    max-width: 300px;
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 15px;
    margin: 20px auto;
    display: block;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 3px solid #fff;
}

.question-text {
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    color: #333;
    margin: 15px 0;
    font-weight: bold;
}

.question-text-en {
    font-size: clamp(1rem, 2.5vw, 1.4rem);
    color: #666;
    margin: 10px 0;
    font-family: Arial, sans-serif;
    font-weight: bold;
    font-style: normal;
}

.speak-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.speak-btn {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.speak-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
}

.speak-btn.speaking {
    animation: pulse 1s infinite;
    background: linear-gradient(45deg, #ff6b35, #e55a2b);
}

.options-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 30px 0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.option-btn {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border: 3px solid #e9ecef;
    border-radius: 15px;
    padding: 20px;
    font-size: clamp(1rem, 2vw, 1.3rem);
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-btn:hover {
    transform: translateY(-3px) scale(1.02);
    border-color: #4CAF50;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.option-btn:disabled {
    cursor: not-allowed;
    transform: none;
}

.letter-option {
    font-size: 3rem;
    font-weight: bold;
    min-height: 100px;
}

.color-option {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.number-option {
    font-size: 1.4rem;
}

.progress-text {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin: 20px 0;
    font-weight: bold;
}

.visual-count {
    background: #f0f8ff;
    border-radius: 15px;
    padding: 20px;
    margin: 20px auto;
    max-width: 500px;
    border: 2px solid #e3f2fd;
    text-align: center;
    word-wrap: break-word;
    line-height: 1.2;
}

.color-display {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.color-display:hover {
    transform: scale(1.05);
}

.emoji-display {
    animation: bounce 2s infinite;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    user-select: none;
}

.emoji-display:hover {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* أنماط خاصة بكل لعبة */
.alphabet-game h2,
.colors-game h2,
.numbers-game h2,
.generic-game h2 {
    text-align: center;
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    color: #333;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* تأثيرات الإجابة الصحيحة والخاطئة */
.correct-answer {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
    border-color: #4CAF50 !important;
    animation: correctPulse 0.6s ease;
}

.wrong-answer {
    background: linear-gradient(135deg, #f44336, #d32f2f) !important;
    color: white !important;
    border-color: #f44336 !important;
    animation: wrongShake 0.6s ease;
}

@keyframes correctPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes wrongShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تجاوب الشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 5px;
    }

    .screen {
        padding: 10px;
    }

    .welcome-content, .level-content, .results-content {
        margin: 10px auto;
        padding: 20px 15px;
    }

    .levels-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .game-header {
        flex-direction: column;
        text-align: center;
    }

    .progress-info {
        margin: 10px 0;
    }

    .results-actions {
        flex-direction: column;
        align-items: center;
    }

    .start-btn, .back-btn, .play-again-btn, .next-level-btn {
        width: 100%;
        max-width: 300px;
    }

    .options-container {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .question-image {
        max-width: 250px;
        height: 150px;
    }

    .speak-container {
        flex-direction: column;
        align-items: center;
    }

    .speak-btn {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .floating-element {
        font-size: 1.5rem;
    }

    .level-card {
        padding: 20px 15px;
    }

    .level-icon {
        font-size: 2.5rem;
    }

    .game-content {
        padding: 20px 15px;
    }

    .question-container {
        padding: 15px;
    }

    .question-image {
        max-width: 200px;
        height: 120px;
    }

    .letter-option {
        font-size: 2rem;
        min-height: 80px;
    }

    .visual-count {
        font-size: 1.8rem;
        padding: 15px;
        max-width: 300px;
        line-height: 1.1;
    }

    .emoji-display {
        font-size: 3rem !important;
    }

    .question-text {
        font-size: 1.3rem !important;
    }

    .question-text-en {
        font-size: 1.1rem !important;
    }
}

/* أنماط لعبة التوصيل */
.matching-game {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.game-instruction {
    text-align: center;
    font-size: 1.2rem;
    color: #333;
    margin: 10px 0;
    font-weight: bold;
}

.game-instruction-en {
    text-align: center;
    font-size: 1rem;
    color: #666;
    margin-bottom: 20px;
    font-style: italic;
}

.matching-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
    min-height: 400px;
}

.words-column, .images-column {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.words-column h3, .images-column h3 {
    text-align: center;
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.draggable-words {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.draggable-word {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 12px;
    cursor: grab;
    user-select: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
}

.draggable-word:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.draggable-word:active {
    cursor: grabbing;
}

.draggable-word.dragging {
    opacity: 0.8;
    transform: rotate(5deg) scale(1.05);
    z-index: 1000;
}

.word-en {
    font-size: 1.1rem;
    font-weight: bold;
}

.word-ar {
    font-size: 1rem;
    opacity: 0.9;
}

.word-speak-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.word-speak-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.drop-zones {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.drop-zone {
    background: rgba(255, 255, 255, 0.9);
    border: 3px dashed #dee2e6;
    border-radius: 15px;
    padding: 15px;
    min-height: 120px;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.drop-zone.drag-over {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    transform: scale(1.02);
}

.drop-zone.correct-match {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(69, 160, 73, 0.2));
    animation: correctMatch 0.6s ease;
}

@keyframes correctMatch {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.family-image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.family-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e9ecef;
}

.image-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.drop-indicator {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

.draggable-word.matched {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    cursor: default;
    transform: none;
    margin-top: 10px;
    font-size: 0.9rem;
    padding: 8px 12px;
}

.game-progress {
    text-align: center;
    margin-top: 30px;
}

.progress-text {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 10px;
    font-weight: bold;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 auto;
    max-width: 400px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.5s ease;
    border-radius: 10px;
}

.shake-animation {
    animation: shake 0.6s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

/* تجاوب مع الشاشات الصغيرة للعبة التوصيل */
@media (max-width: 768px) {
    .matching-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .draggable-word {
        padding: 12px;
        font-size: 0.9rem;
    }

    .drop-zone {
        min-height: 100px;
        padding: 10px;
    }

    .family-image {
        width: 50px;
        height: 50px;
    }
}

/* أنماط لعبة التلوين */
.coloring-game {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.coloring-container {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 30px;
    margin: 30px 0;
    align-items: start;
}

.shape-info {
    text-align: center;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.shape-emoji {
    font-size: 4rem;
    margin-bottom: 15px;
}

.shape-name {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.coloring-area {
    text-align: center;
}

.shape-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

#coloring-canvas {
    border: 3px solid #dee2e6;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#coloring-canvas:hover {
    border-color: #4CAF50;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

.coloring-hint {
    margin-top: 10px;
    font-size: 0.9rem;
    color: #6c757d;
    font-style: italic;
}

.color-palette {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.color-palette h4 {
    text-align: center;
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.colors-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.color-btn {
    width: 100%;
    height: 60px;
    border: 3px solid #dee2e6;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    color: white;
}

.color-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: #4CAF50;
}

.color-btn.selected {
    border-color: #4CAF50;
    border-width: 4px;
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
}

.color-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.color-name {
    font-size: 0.9rem;
    text-align: center;
}

.coloring-progress {
    text-align: center;
    margin-top: 30px;
}

/* تجاوب مع الشاشات الصغيرة للعبة التلوين */
@media (max-width: 768px) {
    .coloring-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    #coloring-canvas {
        width: 250px;
        height: 250px;
    }

    .colors-grid {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 8px;
    }

    .color-btn {
        height: 50px;
        font-size: 0.8rem;
    }

    .shape-emoji {
        font-size: 3rem;
    }
}

/* أنماط لعبة ترتيب الأرقام */
.sequence-game {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.sequence-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    margin: 30px 0;
}

.numbers-pool {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.numbers-pool h3 {
    text-align: center;
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.draggable-numbers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.draggable-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
    border-radius: 12px;
    cursor: grab;
    user-select: none;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
}

.draggable-number:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.draggable-number:active {
    cursor: grabbing;
}

.draggable-number.dragging {
    opacity: 0.8;
    transform: rotate(5deg) scale(1.05);
    z-index: 1000;
}

.number-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.number-word {
    font-size: 0.8rem;
    opacity: 0.9;
    margin-bottom: 5px;
}

.number-speak-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.number-speak-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.sequence-line {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.sequence-line h3 {
    text-align: center;
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.sequence-slots {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.sequence-slot {
    background: rgba(255, 255, 255, 0.9);
    border: 3px dashed #dee2e6;
    border-radius: 12px;
    padding: 15px;
    min-height: 80px;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.sequence-slot.drag-over {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    transform: scale(1.02);
}

.sequence-slot.correct-position {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.2);
    animation: correctPosition 0.6s ease;
}

.sequence-slot.incorrect-position {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.2);
    animation: incorrectPosition 0.6s ease;
}

.sequence-slot.final-correct {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(69, 160, 73, 0.3));
}

.sequence-slot.final-incorrect {
    border-color: #f44336;
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.3), rgba(211, 47, 47, 0.3));
}

@keyframes correctPosition {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes incorrectPosition {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

.slot-number {
    font-size: 1.2rem;
    font-weight: bold;
    color: #6c757d;
    margin-bottom: 5px;
}

.slot-indicator {
    font-size: 0.7rem;
    color: #6c757d;
    font-style: italic;
    text-align: center;
}

.placed-number {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    cursor: default;
    transform: none;
    margin: 0;
    padding: 8px;
    font-size: 0.9rem;
}

.sequence-progress {
    text-align: center;
    margin-top: 30px;
}

.check-sequence-btn {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.check-sequence-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.check-sequence-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* تجاوب مع الشاشات الصغيرة للعبة ترتيب الأرقام */
@media (max-width: 768px) {
    .sequence-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .sequence-slots {
        grid-template-columns: repeat(5, 1fr);
        gap: 5px;
    }

    .sequence-slot {
        min-height: 60px;
        padding: 8px;
    }

    .draggable-numbers {
        grid-template-columns: repeat(5, 1fr);
        gap: 5px;
    }

    .draggable-number {
        padding: 8px;
        font-size: 0.8rem;
    }

    .number-value {
        font-size: 1.2rem;
    }
}

/* أنماط لعبة الذاكرة */
.memory-game {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.memory-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 20px 0;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    padding: 15px;
    border: 2px solid #e9ecef;
}

.stat-item {
    text-align: center;
}

.stat-label {
    font-size: 1rem;
    color: #6c757d;
    display: block;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.stat-total {
    font-size: 1.2rem;
    color: #6c757d;
}

.memory-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin: 30px 0;
    perspective: 1000px;
}

.memory-card {
    aspect-ratio: 1;
    cursor: pointer;
    position: relative;
    transition: transform 0.3s ease;
}

.memory-card:hover {
    transform: scale(1.05);
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
    border-radius: 12px;
}

.memory-card.flipped .card-inner {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.card-front {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.card-back {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    transform: rotateY(180deg);
    padding: 10px;
}

.card-question {
    font-size: 3rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.card-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.card-emoji {
    font-size: 2.5rem;
}

.card-image img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid white;
}

.card-word {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.word-en {
    font-size: 1.2rem;
    font-weight: bold;
}

.word-ar {
    font-size: 1rem;
    opacity: 0.9;
}

.card-speak-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-top: 5px;
}

.card-speak-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.memory-card.matched .card-inner {
    transform: rotateY(180deg);
}

.memory-card.matched .card-back {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    animation: matchSuccess 0.8s ease;
}

.memory-card.game-complete {
    animation: gameComplete 1s ease infinite alternate;
}

@keyframes matchSuccess {
    0% { transform: rotateY(180deg) scale(1); }
    50% { transform: rotateY(180deg) scale(1.1); }
    100% { transform: rotateY(180deg) scale(1); }
}

@keyframes gameComplete {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

.memory-progress {
    text-align: center;
    margin-top: 30px;
}

/* تجاوب مع الشاشات الصغيرة للعبة الذاكرة */
@media (max-width: 768px) {
    .memory-board {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .memory-stats {
        gap: 20px;
        padding: 10px;
    }

    .stat-value {
        font-size: 1.3rem;
    }

    .card-question {
        font-size: 2rem;
    }

    .card-emoji {
        font-size: 2rem;
    }

    .card-image img {
        width: 40px;
        height: 40px;
    }

    .word-en {
        font-size: 1rem;
    }

    .word-ar {
        font-size: 0.9rem;
    }
}
