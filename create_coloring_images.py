#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create simple coloring images for the coloring game
"""

from PIL import Image, ImageDraw
import os

def create_apple_outline():
    """Create apple outline image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Apple body
    draw.ellipse([75, 100, 225, 250], outline='black', width=3, fill='white')
    
    # Apple stem
    draw.rectangle([145, 80, 155, 100], outline='black', width=2, fill='white')
    
    # Apple leaf
    draw.ellipse([160, 85, 180, 105], outline='black', width=2, fill='white')
    
    return img

def create_apple_colored():
    """Create colored apple image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Apple body
    draw.ellipse([75, 100, 225, 250], outline='black', width=3, fill='red')
    
    # Apple stem
    draw.rectangle([145, 80, 155, 100], outline='black', width=2, fill='brown')
    
    # Apple leaf
    draw.ellipse([160, 85, 180, 105], outline='black', width=2, fill='green')
    
    return img

def create_sun_outline():
    """Create sun outline image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Sun center
    draw.ellipse([100, 100, 200, 200], outline='black', width=3, fill='white')
    
    # Sun rays
    rays = [
        (150, 50, 150, 90),   # top
        (150, 210, 150, 250), # bottom
        (50, 150, 90, 150),   # left
        (210, 150, 250, 150), # right
        (100, 100, 80, 80),   # top-left
        (200, 100, 220, 80),  # top-right
        (100, 200, 80, 220),  # bottom-left
        (200, 200, 220, 220)  # bottom-right
    ]
    
    for ray in rays:
        draw.line(ray, fill='black', width=3)
    
    return img

def create_sun_colored():
    """Create colored sun image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Sun center
    draw.ellipse([100, 100, 200, 200], outline='black', width=3, fill='yellow')
    
    # Sun rays
    rays = [
        (150, 50, 150, 90),   # top
        (150, 210, 150, 250), # bottom
        (50, 150, 90, 150),   # left
        (210, 150, 250, 150), # right
        (100, 100, 80, 80),   # top-left
        (200, 100, 220, 80),  # top-right
        (100, 200, 80, 220),  # bottom-left
        (200, 200, 220, 220)  # bottom-right
    ]
    
    for ray in rays:
        draw.line(ray, fill='orange', width=3)
    
    return img

def create_tree_outline():
    """Create tree outline image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Tree trunk
    draw.rectangle([135, 180, 165, 250], outline='black', width=3, fill='white')
    
    # Tree leaves (circle)
    draw.ellipse([80, 80, 220, 200], outline='black', width=3, fill='white')
    
    return img

def create_tree_colored():
    """Create colored tree image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Tree trunk
    draw.rectangle([135, 180, 165, 250], outline='black', width=3, fill='brown')
    
    # Tree leaves (circle)
    draw.ellipse([80, 80, 220, 200], outline='black', width=3, fill='green')
    
    return img

def create_orange_outline():
    """Create orange outline image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Orange body
    draw.ellipse([75, 75, 225, 225], outline='black', width=3, fill='white')
    
    # Orange segments
    center = (150, 150)
    for i in range(6):
        angle = i * 60
        x = center[0] + 60 * (1 if angle % 180 == 0 else 0.5)
        y = center[1] + 60 * (1 if angle % 180 == 90 else 0.5)
        draw.line([center[0], center[1], x, y], fill='black', width=2)
    
    return img

def create_orange_colored():
    """Create colored orange image"""
    img = Image.new('RGBA', (300, 300), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Orange body
    draw.ellipse([75, 75, 225, 225], outline='black', width=3, fill='orange')
    
    # Orange segments
    center = (150, 150)
    for i in range(6):
        angle = i * 60
        x = center[0] + 60 * (1 if angle % 180 == 0 else 0.5)
        y = center[1] + 60 * (1 if angle % 180 == 90 else 0.5)
        draw.line([center[0], center[1], x, y], fill='darkorange', width=2)
    
    return img

def main():
    """Create all coloring images"""
    # Create images directory if it doesn't exist
    os.makedirs('images', exist_ok=True)
    
    # Create all images
    images = {
        'coloring_apple_outline.png': create_apple_outline(),
        'coloring_apple_colored.png': create_apple_colored(),
        'coloring_sun_outline.png': create_sun_outline(),
        'coloring_sun_colored.png': create_sun_colored(),
        'coloring_tree_outline.png': create_tree_outline(),
        'coloring_tree_colored.png': create_tree_colored(),
        'coloring_orange_outline.png': create_orange_outline(),
        'coloring_orange_colored.png': create_orange_colored(),
    }
    
    for filename, img in images.items():
        filepath = os.path.join('images', filename)
        img.save(filepath)
        print(f"Created: {filepath}")
    
    print("All coloring images created successfully!")

if __name__ == "__main__":
    main()
