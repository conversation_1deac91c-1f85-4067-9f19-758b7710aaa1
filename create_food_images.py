#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create simple food images for the categorization game
"""

from PIL import Image, ImageDraw
import os

def create_carrot():
    """Create carrot image"""
    img = Image.new('RGBA', (200, 200), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Carrot body (orange triangle)
    points = [(100, 50), (70, 150), (130, 150)]
    draw.polygon(points, fill='orange', outline='darkorange', width=2)
    
    # Carrot top (green leaves)
    draw.ellipse([85, 30, 115, 60], fill='green', outline='darkgreen', width=2)
    draw.ellipse([90, 25, 110, 55], fill='lightgreen')
    
    return img

def create_tomato():
    """Create tomato image"""
    img = Image.new('RGBA', (200, 200), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Tomato body (red circle)
    draw.ellipse([50, 70, 150, 170], fill='red', outline='darkred', width=2)
    
    # <PERSON><PERSON> top (green stem)
    draw.ellipse([90, 50, 110, 80], fill='green', outline='darkgreen', width=2)
    
    return img

def create_lettuce():
    """Create lettuce image"""
    img = Image.new('RGBA', (200, 200), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Lettuce leaves (green circles)
    draw.ellipse([60, 60, 140, 140], fill='lightgreen', outline='green', width=2)
    draw.ellipse([70, 70, 130, 130], fill='palegreen')
    
    return img

def create_orange():
    """Create orange image"""
    img = Image.new('RGBA', (200, 200), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Orange body
    draw.ellipse([50, 50, 150, 150], fill='orange', outline='darkorange', width=2)
    
    # Orange texture lines
    for i in range(6):
        angle = i * 60
        x = 100 + 40 * (1 if angle % 180 == 0 else 0.5)
        y = 100 + 40 * (1 if angle % 180 == 90 else 0.5)
        draw.line([100, 100, x, y], fill='darkorange', width=1)
    
    return img

def create_juice():
    """Create juice image"""
    img = Image.new('RGBA', (200, 200), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # Glass
    draw.rectangle([70, 60, 130, 160], fill='lightblue', outline='blue', width=2)
    
    # Juice
    draw.rectangle([75, 80, 125, 155], fill='orange')
    
    # Straw
    draw.rectangle([115, 40, 120, 80], fill='red')
    
    return img

def main():
    """Create all food images"""
    # Create images directory if it doesn't exist
    os.makedirs('images', exist_ok=True)
    
    # Create all images
    images = {
        'carrot.png': create_carrot(),
        'tomato.png': create_tomato(),
        'lettuce.png': create_lettuce(),
        'orange.png': create_orange(),
        'juice.png': create_juice(),
    }
    
    for filename, img in images.items():
        filepath = os.path.join('images', filename)
        img.save(filepath)
        print(f"Created: {filepath}")
    
    print("All food images created successfully!")

if __name__ == "__main__":
    main()
