// بيانات المستوى الأول - الصف الأول الابتدائي

// بيانات لعبة الحروف الأبجدية
const alphabetData = [
    { letter: 'A', word: 'Apple', arabic: 'تفاحة', image: 'images/apple.png', emoji: '🍎' },
    { letter: 'B', word: 'Ball', arabic: 'كرة', image: 'images/ball.png', emoji: '⚽' },
    { letter: 'C', word: 'Cat', arabic: 'قطة', image: 'images/cat.png', emoji: '🐱' },
    { letter: 'D', word: 'Dog', arabic: 'كلب', image: 'images/dog.png', emoji: '🐶' },
    { letter: 'E', word: 'Elephant', arabic: 'فيل', image: 'images/elephant.png', emoji: '🐘' },
    { letter: 'F', word: 'Fish', arabic: 'سمكة', image: 'images/fish.png', emoji: '🐟' },
    { letter: 'G', word: 'Giraffe', arabic: 'زرافة', image: 'images/giraffe.png', emoji: '🦒' },
    { letter: 'H', word: 'House', arabic: 'منزل', image: 'images/house.png', emoji: '🏠' }
];

// بيانات لعبة الألوان
const colorsData = [
    { color: 'Red', arabic: 'أحمر', hex: '#FF0000', items: ['Apple', 'Rose', 'Fire truck'] },
    { color: 'Blue', arabic: 'أزرق', hex: '#0000FF', items: ['Sky', 'Ocean', 'Blueberry'] },
    { color: 'Yellow', arabic: 'أصفر', hex: '#FFFF00', items: ['Sun', 'Banana', 'Lemon'] },
    { color: 'Green', arabic: 'أخضر', hex: '#00FF00', items: ['Grass', 'Tree', 'Frog'] },
    { color: 'Orange', arabic: 'برتقالي', hex: '#FFA500', items: ['Orange', 'Carrot', 'Pumpkin'] },
    { color: 'Purple', arabic: 'بنفسجي', hex: '#800080', items: ['Grapes', 'Flower', 'Eggplant'] },
    { color: 'Black', arabic: 'أسود', hex: '#000000', items: ['Night', 'Ant', 'Coal'] },
    { color: 'White', arabic: 'أبيض', hex: '#FFFFFF', items: ['Snow', 'Cloud', 'Milk'] }
];

// بيانات لعبة الأرقام
const numbersData = [
    { number: 1, word: 'One', arabic: 'واحد', items: 1 },
    { number: 2, word: 'Two', arabic: 'اثنان', items: 2 },
    { number: 3, word: 'Three', arabic: 'ثلاثة', items: 3 },
    { number: 4, word: 'Four', arabic: 'أربعة', items: 4 },
    { number: 5, word: 'Five', arabic: 'خمسة', items: 5 },
    { number: 6, word: 'Six', arabic: 'ستة', items: 6 },
    { number: 7, word: 'Seven', arabic: 'سبعة', items: 7 },
    { number: 8, word: 'Eight', arabic: 'ثمانية', items: 8 },
    { number: 9, word: 'Nine', arabic: 'تسعة', items: 9 },
    { number: 10, word: 'Ten', arabic: 'عشرة', items: 10 }
];

// بيانات لعبة العائلة
const familyData = [
    { word: 'Father', arabic: 'أب', image: 'images/father.png', description: 'Dad', emoji: '👨' },
    { word: 'Mother', arabic: 'أم', image: 'images/mother.png', description: 'Mom', emoji: '👩' },
    { word: 'Brother', arabic: 'أخ', image: 'images/brother.png', description: 'Boy sibling', emoji: '👦' },
    { word: 'Sister', arabic: 'أخت', image: 'images/sister.png', description: 'Girl sibling', emoji: '👧' },
    { word: 'Grandfather', arabic: 'جد', image: 'images/grandfather.png', description: 'Grandpa', emoji: '👴' },
    { word: 'Grandmother', arabic: 'جدة', image: 'images/grandmother.png', description: 'Grandma', emoji: '👵' }
];

// بيانات لعبة أجزاء الجسم
const bodyPartsData = [
    { word: 'Head', arabic: 'رأس', image: 'images/head.png', emoji: '🗣️' },
    { word: 'Eye', arabic: 'عين', image: 'images/eye.png', emoji: '👁️' },
    { word: 'Ear', arabic: 'أذن', image: 'images/ear.png', emoji: '👂' },
    { word: 'Nose', arabic: 'أنف', image: 'images/nose.png', emoji: '👃' },
    { word: 'Mouth', arabic: 'فم', image: 'images/mouth.png', emoji: '👄' },
    { word: 'Hand', arabic: 'يد', image: 'images/hand.png', emoji: '✋' },
    { word: 'Foot', arabic: 'قدم', image: 'images/foot.png', emoji: '🦶' }
];

// بيانات لعبة الحيوانات
const animalsData = [
    { word: 'Cat', arabic: 'قطة', image: 'images/cat.png', sound: 'Meow', emoji: '🐱' },
    { word: 'Dog', arabic: 'كلب', image: 'images/dog.png', sound: 'Woof', emoji: '🐶' },
    { word: 'Bird', arabic: 'طائر', image: 'images/bird.png', sound: 'Tweet', emoji: '🐦' },
    { word: 'Elephant', arabic: 'فيل', image: 'images/elephant.png', sound: 'Trumpet', emoji: '🐘' },
    { word: 'Monkey', arabic: 'قرد', image: 'images/monkey.png', sound: 'Ooh ooh', emoji: '🐵' },
    { word: 'Lion', arabic: 'أسد', image: 'images/lion.png', sound: 'Roar', emoji: '🦁' }
];

// بيانات لعبة الطعام والتصنيف
const foodData = [
    // فواكه
    { word: 'Apple', arabic: 'تفاحة', image: 'images/apple.png', category: 'fruits', emoji: '🍎' },
    { word: 'Banana', arabic: 'موزة', image: 'images/banana.png', category: 'fruits', emoji: '🍌' },
    { word: 'Orange', arabic: 'برتقالة', image: 'images/orange.png', category: 'fruits', emoji: '🍊' },

    // خضروات
    { word: 'Carrot', arabic: 'جزر', image: 'images/carrot.png', category: 'vegetables', emoji: '🥕' },
    { word: 'Tomato', arabic: 'طماطم', image: 'images/tomato.png', category: 'vegetables', emoji: '🍅' },
    { word: 'Lettuce', arabic: 'خس', image: 'images/lettuce.png', category: 'vegetables', emoji: '🥬' },

    // مشروبات
    { word: 'Water', arabic: 'ماء', image: 'images/water.png', category: 'drinks', emoji: '💧' },
    { word: 'Milk', arabic: 'حليب', image: 'images/milk.png', category: 'drinks', emoji: '🥛' },
    { word: 'Juice', arabic: 'عصير', image: 'images/juice.png', category: 'drinks', emoji: '🧃' }
];

// فئات التصنيف
const categories = [
    {
        id: 'fruits',
        name: 'فواكه',
        nameEn: 'Fruits',
        emoji: '🍎',
        color: '#FF6B35',
        description: 'الفواكه الطازجة والحلوة'
    },
    {
        id: 'vegetables',
        name: 'خضروات',
        nameEn: 'Vegetables',
        emoji: '🥕',
        color: '#4CAF50',
        description: 'الخضروات الصحية والمفيدة'
    },
    {
        id: 'drinks',
        name: 'مشروبات',
        nameEn: 'Drinks',
        emoji: '🥛',
        color: '#2196F3',
        description: 'المشروبات المنعشة والمفيدة'
    }
];

// وظائف ألعاب المستوى الأول

function loadAlphabetGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(alphabetData).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(alphabetData.filter(item => item.letter !== question.letter)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="alphabet-game">
                <h2>🔤 لعبة الحروف الأبجدية</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji}</div>
                    <img src="${question.image}" alt="${question.word}" class="question-image"
                         onload="console.log('Image loaded successfully: ${question.image}')"
                         onerror="console.log('Failed to load image: ${question.image}'); this.src='images/placeholder.jpg'; this.style.border='2px dashed #ff6b35'">
                    <p class="question-text">ما هو الحرف الأول من كلمة "${question.arabic}"؟</p>
                    <p class="question-text-en">What is the first letter of "${question.word}"?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar', '${question.word.toLowerCase()}_ar.mp3')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.word}', 'en', '${question.word.toLowerCase()}_en.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn letter-option" onclick="checkAlphabetAnswer('${option.letter}', '${question.letter}')">
                            ${option.letter}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkAlphabetAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.letter-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.trim() === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.trim() === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

function loadColorsGame() {
    loadColoringGame();
}

function loadNumbersGame() {
    loadNumberSequenceGame();
}

// باقي الألعاب (العائلة، أجزاء الجسم، الحيوانات، الطعام) ستكون بنفس النمط
function loadFamilyGame() {
    loadFamilyMatchingGame();
}

function loadBodyPartsGame() {
    loadBodyPartsPointingGame();
}

function loadAnimalsGame() {
    loadMemoryGame();
}

function loadFoodGame() {
    loadCategorizationGame();
}

// وظيفة عامة للألعاب المتشابهة
function loadGenericGame(data, gameName, emoji, questionAr, questionEn) {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(data).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(data.filter(item => item.word !== question.word)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="generic-game">
                <h2>${emoji} لعبة ${gameName}</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji || '❓'}</div>
                    <img src="${question.image}" alt="${question.word}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">${questionAr}</p>
                    <p class="question-text-en">${questionEn}</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar', '${question.word.toLowerCase()}_ar.mp3')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.word}', 'en', '${question.word.toLowerCase()}_en.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn generic-option" onclick="checkGenericAnswer('${option.word}', '${question.word}')">
                            ${option.word}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkGenericAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.generic-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correct)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.includes(selected) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            enhancedSpeak('عظيم!', 'ar', 'great.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'try_again.mp3');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

// لعبة التوصيل الجديدة - أفراد العائلة
function loadFamilyMatchingGame() {
    let currentQuestionIndex = 0;
    let matchedPairs = 0;
    const totalPairs = 4; // عدد أزواج العائلة
    const gameData = shuffleArray(familyData).slice(0, totalPairs);

    function showMatchingGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="matching-game">
                <h2>👨‍👩‍👧‍👦 لعبة توصيل أفراد العائلة</h2>
                <p class="game-instruction">اسحب الكلمات وضعها على الصور المناسبة</p>
                <p class="game-instruction-en">Drag the words to match them with the correct pictures</p>

                <div class="matching-container">
                    <div class="words-column">
                        <h3>الكلمات</h3>
                        <div class="draggable-words">
                            ${gameData.map((item, index) => `
                                <div class="draggable-word"
                                     draggable="true"
                                     data-word="${item.word}"
                                     data-arabic="${item.arabic}"
                                     id="word-${index}">
                                    <span class="word-en">${item.word}</span>
                                    <span class="word-ar">${item.arabic}</span>
                                    <button class="word-speak-btn" onclick="enhancedSpeak('${item.arabic}', 'ar', '${item.word.toLowerCase()}_ar.mp3')">🔊</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="images-column">
                        <h3>الصور</h3>
                        <div class="drop-zones">
                            ${gameData.map((item, index) => `
                                <div class="drop-zone"
                                     data-correct-word="${item.word}"
                                     id="zone-${index}">
                                    <div class="family-image-container">
                                        <div class="emoji-display">${item.emoji}</div>
                                        <img src="${item.image}" alt="${item.word}" class="family-image"
                                             onerror="this.style.display='none'">
                                        <div class="image-label">${item.description}</div>
                                    </div>
                                    <div class="drop-indicator">ضع الكلمة هنا</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="game-progress">
                    <div class="progress-text">المطابقات: <span id="matches-count">0</span> من ${totalPairs}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupDragAndDrop();
    }

    function setupDragAndDrop() {
        const draggableWords = document.querySelectorAll('.draggable-word');
        const dropZones = document.querySelectorAll('.drop-zone');

        // إعداد الكلمات القابلة للسحب
        draggableWords.forEach(word => {
            word.addEventListener('dragstart', handleDragStart);
            word.addEventListener('dragend', handleDragEnd);

            // دعم اللمس للأجهزة المحمولة
            word.addEventListener('touchstart', handleTouchStart, { passive: false });
            word.addEventListener('touchmove', handleTouchMove, { passive: false });
            word.addEventListener('touchend', handleTouchEnd, { passive: false });
        });

        // إعداد مناطق الإفلات
        dropZones.forEach(zone => {
            zone.addEventListener('dragover', handleDragOver);
            zone.addEventListener('drop', handleDrop);
            zone.addEventListener('dragenter', handleDragEnter);
            zone.addEventListener('dragleave', handleDragLeave);
        });
    }

    let draggedElement = null;
    let touchOffset = { x: 0, y: 0 };

    function handleDragStart(e) {
        draggedElement = e.target;
        e.target.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.target.outerHTML);
    }

    function handleDragEnd(e) {
        e.target.classList.remove('dragging');
        draggedElement = null;
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    }

    function handleDragEnter(e) {
        e.preventDefault();
        e.target.closest('.drop-zone').classList.add('drag-over');
    }

    function handleDragLeave(e) {
        if (!e.target.closest('.drop-zone').contains(e.relatedTarget)) {
            e.target.closest('.drop-zone').classList.remove('drag-over');
        }
    }

    function handleDrop(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.drop-zone');
        dropZone.classList.remove('drag-over');

        if (draggedElement) {
            checkMatch(draggedElement, dropZone);
        }
    }

    // دعم اللمس للأجهزة المحمولة
    function handleTouchStart(e) {
        e.preventDefault();
        draggedElement = e.target;
        const touch = e.touches[0];
        const rect = draggedElement.getBoundingClientRect();
        touchOffset.x = touch.clientX - rect.left;
        touchOffset.y = touch.clientY - rect.top;

        draggedElement.classList.add('dragging');
        draggedElement.style.position = 'fixed';
        draggedElement.style.zIndex = '1000';
        draggedElement.style.pointerEvents = 'none';
    }

    function handleTouchMove(e) {
        e.preventDefault();
        if (!draggedElement) return;

        const touch = e.touches[0];
        draggedElement.style.left = (touch.clientX - touchOffset.x) + 'px';
        draggedElement.style.top = (touch.clientY - touchOffset.y) + 'px';
    }

    function handleTouchEnd(e) {
        e.preventDefault();
        if (!draggedElement) return;

        const touch = e.changedTouches[0];
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
        const dropZone = elementBelow ? elementBelow.closest('.drop-zone') : null;

        // إعادة تعيين الموضع
        draggedElement.style.position = '';
        draggedElement.style.zIndex = '';
        draggedElement.style.pointerEvents = '';
        draggedElement.style.left = '';
        draggedElement.style.top = '';
        draggedElement.classList.remove('dragging');

        if (dropZone) {
            checkMatch(draggedElement, dropZone);
        }

        draggedElement = null;
    }

    function checkMatch(wordElement, dropZone) {
        const wordData = wordElement.dataset.word;
        const correctWord = dropZone.dataset.correctWord;
        const isCorrect = wordData === correctWord;

        if (isCorrect) {
            // مطابقة صحيحة
            handleCorrectMatch(wordElement, dropZone);
        } else {
            // مطابقة خاطئة
            handleIncorrectMatch(wordElement);
        }
    }

    function handleCorrectMatch(wordElement, dropZone) {
        // إخفاء مؤشر الإفلات
        const dropIndicator = dropZone.querySelector('.drop-indicator');
        dropIndicator.style.display = 'none';

        // إضافة الكلمة إلى منطقة الإفلات
        const matchedWord = wordElement.cloneNode(true);
        matchedWord.classList.add('matched');
        matchedWord.draggable = false;
        dropZone.appendChild(matchedWord);

        // إخفاء الكلمة الأصلية
        wordElement.style.display = 'none';

        // تسجيل النقاط والتقدم
        matchedPairs++;
        recordAnswer(true);
        updateProgress();

        // تأثيرات بصرية وصوتية
        dropZone.classList.add('correct-match');
        enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
        celebrateCorrectAnswer();

        // التحقق من اكتمال اللعبة
        if (matchedPairs >= totalPairs) {
            setTimeout(() => {
                completeMatchingGame();
            }, 1500);
        }
    }

    function handleIncorrectMatch(wordElement) {
        // تأثير اهتزاز للكلمة
        wordElement.classList.add('shake-animation');
        enhancedSpeak('حاول مرة أخرى', 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            wordElement.classList.remove('shake-animation');
        }, 600);
    }

    function updateProgress() {
        const matchesCount = document.getElementById('matches-count');
        const progressFill = document.getElementById('progress-fill');

        if (matchesCount) {
            matchesCount.textContent = matchedPairs;
        }

        if (progressFill) {
            const percentage = (matchedPairs / totalPairs) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeMatchingGame() {
        enhancedSpeak('ممتاز! أكملت جميع المطابقات!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showMatchingGame();
}

// لعبة التلوين الجديدة
function loadColoringGame() {
    let currentShapeIndex = 0;

    // بيانات الأشكال للتلوين
    const coloringShapes = [
        {
            name: 'Apple',
            arabic: 'تفاحة',
            correctColor: 'Red',
            correctColorAr: 'أحمر',
            correctHex: '#FF0000',
            outlineImage: 'images/coloring_apple_outline.png',
            coloredImage: 'images/coloring_apple_colored.png',
            emoji: '🍎'
        },
        {
            name: 'Sun',
            arabic: 'شمس',
            correctColor: 'Yellow',
            correctColorAr: 'أصفر',
            correctHex: '#FFFF00',
            outlineImage: 'images/coloring_sun_outline.png',
            coloredImage: 'images/coloring_sun_colored.png',
            emoji: '☀️'
        },
        {
            name: 'Tree',
            arabic: 'شجرة',
            correctColor: 'Green',
            correctColorAr: 'أخضر',
            correctHex: '#00FF00',
            outlineImage: 'images/coloring_tree_outline.png',
            coloredImage: 'images/coloring_tree_colored.png',
            emoji: '🌳'
        },
        {
            name: 'Orange',
            arabic: 'برتقالة',
            correctColor: 'Orange',
            correctColorAr: 'برتقالي',
            correctHex: '#FFA500',
            outlineImage: 'images/coloring_orange_outline.png',
            coloredImage: 'images/coloring_orange_colored.png',
            emoji: '🍊'
        }
    ];

    // ألوان متاحة للتلوين
    const availableColors = [
        { name: 'Red', arabic: 'أحمر', hex: '#FF0000' },
        { name: 'Yellow', arabic: 'أصفر', hex: '#FFFF00' },
        { name: 'Green', arabic: 'أخضر', hex: '#00FF00' },
        { name: 'Orange', arabic: 'برتقالي', hex: '#FFA500' },
        { name: 'Blue', arabic: 'أزرق', hex: '#0000FF' },
        { name: 'Purple', arabic: 'بنفسجي', hex: '#800080' }
    ];

    function showColoringGame() {
        if (currentShapeIndex >= coloringShapes.length) {
            completeColoringGame();
            return;
        }

        const currentShape = coloringShapes[currentShapeIndex];

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="coloring-game">
                <h2>🎨 لعبة التلوين</h2>
                <p class="game-instruction">اختر اللون المناسب لتلوين الشكل</p>
                <p class="game-instruction-en">Choose the right color to paint the shape</p>

                <div class="coloring-container">
                    <div class="shape-info">
                        <div class="shape-emoji">${currentShape.emoji}</div>
                        <h3 class="shape-name">${currentShape.arabic} - ${currentShape.name}</h3>
                        <div class="speak-container">
                            <button class="speak-btn" onclick="enhancedSpeak('${currentShape.arabic}', 'ar', '${currentShape.name.toLowerCase()}_ar.wav')">🔊 العربية</button>
                            <button class="speak-btn" onclick="enhancedSpeak('${currentShape.name}', 'en', '${currentShape.name.toLowerCase()}_en.wav')">🔊 English</button>
                        </div>
                    </div>

                    <div class="coloring-area">
                        <div class="image-container">
                            <img id="coloring-image"
                                 src="${currentShape.outlineImage}"
                                 alt="${currentShape.name}"
                                 class="coloring-image clickable-image"
                                 data-correct-color="${currentShape.correctColor}"
                                 data-colored-image="${currentShape.coloredImage}">
                            <div class="coloring-hint">اختر اللون ${currentShape.correctColorAr} ثم انقر على الصورة</div>
                        </div>
                    </div>

                    <div class="color-palette">
                        <h4>اختر اللون:</h4>
                        <div class="colors-grid">
                            ${availableColors.map(color => `
                                <button class="color-btn"
                                        data-color="${color.name}"
                                        data-hex="${color.hex}"
                                        data-arabic="${color.arabic}"
                                        style="background-color: ${color.hex}">
                                    <span class="color-name">${color.arabic}</span>
                                </button>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="coloring-progress">
                    <div class="progress-text">الشكل ${currentShapeIndex + 1} من ${coloringShapes.length}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${(currentShapeIndex / coloringShapes.length) * 100}%"></div>
                    </div>
                </div>
            </div>
        `;

        setupImageColoring(currentShape);
        setupColorButtons();
    }

    function setupColorButtons() {
        const colorButtons = document.querySelectorAll('.color-btn');

        colorButtons.forEach(button => {
            // إزالة أي أحداث سابقة
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
        });

        // إعادة الحصول على الأزرار الجديدة
        const freshColorButtons = document.querySelectorAll('.color-btn');

        freshColorButtons.forEach(button => {
            function handleColorSelection() {
                const colorName = button.getAttribute('data-color');
                const colorHex = button.getAttribute('data-hex');
                const colorArabic = button.getAttribute('data-arabic');
                console.log('Color selected:', colorName, colorHex, colorArabic);
                selectColor(colorName, colorHex, colorArabic);
            }

            // إضافة أحداث النقر واللمس
            button.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Color button clicked');
                handleColorSelection();
            });

            button.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Color button touched');
                handleColorSelection();
            });

            // إضافة حدث touchstart للتأكد من التفاعل
            button.addEventListener('touchstart', function(e) {
                e.preventDefault();
                console.log('Touch started on color button');
            });
        });
    }

    let selectedColor = null;
    let selectedColorHex = null;
    let selectedColorArabic = null;

    window.selectColor = function(colorName, colorHex, colorArabic) {
        selectedColor = colorName;
        selectedColorHex = colorHex;
        selectedColorArabic = colorArabic;

        // تحديث مظهر الأزرار
        document.querySelectorAll('.color-btn').forEach(btn => {
            btn.classList.remove('selected');
        });

        const selectedBtn = document.querySelector(`[data-color="${colorName}"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('selected');
        }

        // تشغيل صوت اللون
        enhancedSpeak(colorArabic, 'ar', `${colorName.toLowerCase()}_ar.wav`);

        // تحديث مؤشر الماوس للصورة
        const image = document.getElementById('coloring-image');
        if (image) {
            image.style.cursor = selectedColor ? 'pointer' : 'default';
        }
    };

    // وظيفة جديدة للتحقق من التلوين
    window.tryColoring = function(correctColor, coloredImageSrc) {
        console.log('tryColoring called with:', correctColor, coloredImageSrc, 'selectedColor:', selectedColor);
        if (!selectedColor) {
            console.log('No color selected, showing message');
            enhancedSpeak('اختر لوناً أولاً', 'ar', 'try_again.wav');

            // تأثير تنبيه للوحة الألوان
            const colorPalette = document.querySelector('.color-palette');
            if (colorPalette) {
                colorPalette.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    colorPalette.style.animation = '';
                }, 600);
            }
            return;
        }

        checkColorChoice(correctColor, coloredImageSrc);
    };

    function setupImageColoring(shape) {
        const image = document.getElementById('coloring-image');

        // إزالة أي أحداث سابقة
        const newImage = image.cloneNode(true);
        image.parentNode.replaceChild(newImage, image);

        // الحصول على المرجع الجديد
        const freshImage = document.getElementById('coloring-image');

        // وظيفة التلوين الموحدة
        function handleColoring() {
            console.log('handleColoring called - selectedColor:', selectedColor);

            // التأكد من وجود لون مختار
            if (!selectedColor) {
                console.log('No color selected, showing alert');
                enhancedSpeak('اختر لوناً أولاً', 'ar', 'try_again.wav');
                return;
            }

            const correctColor = freshImage.getAttribute('data-correct-color');
            const coloredImageSrc = freshImage.getAttribute('data-colored-image');
            console.log('correctColor:', correctColor, 'coloredImageSrc:', coloredImageSrc);

            // استدعاء مباشر لـ checkColorChoice بدلاً من tryColoring
            checkColorChoice(correctColor, coloredImageSrc);
        }

        // إضافة أحداث النقر واللمس
        freshImage.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Image clicked');
            handleColoring();
        });

        freshImage.addEventListener('touchend', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Image touched');
            handleColoring();
        });

        // إضافة حدث إضافي للأجهزة المحمولة
        freshImage.addEventListener('touchstart', function(e) {
            e.preventDefault();
            console.log('Touch started on image');
        });

        // تأثيرات التفاعل للكمبيوتر
        freshImage.addEventListener('mouseenter', function() {
            if (selectedColor) {
                this.style.cursor = 'pointer';
                this.style.filter = 'brightness(1.1)';
                this.style.transform = 'scale(1.02)';
            }
        });

        freshImage.addEventListener('mouseleave', function() {
            this.style.filter = 'brightness(1)';
            this.style.transform = 'scale(1)';
        });

        // تأثيرات التفاعل للهاتف (منفصلة عن touchstart الخاص بالتلوين)
        freshImage.addEventListener('touchmove', function(e) {
            e.preventDefault(); // منع التمرير
        });

        freshImage.addEventListener('touchcancel', function(e) {
            this.style.filter = 'brightness(1)';
            this.style.transform = 'scale(1)';
        });
    }

    function checkColorChoice(correctColor, coloredImageSrc) {
        console.log('checkColorChoice called - isCorrect:', selectedColor === correctColor);
        const isCorrect = selectedColor === correctColor;
        recordAnswer(isCorrect);

        const image = document.getElementById('coloring-image');

        if (isCorrect) {
            // إجابة صحيحة - تغيير الصورة إلى الملونة
            console.log('Changing image src to:', coloredImageSrc);
            image.src = coloredImageSrc;
            image.style.animation = 'coloringSuccess 1s ease-in-out';

            // إضافة معالجة خاصة للأجهزة المحمولة
            image.onload = function() {
                console.log('New image loaded successfully');
                this.style.transform = 'scale(1.05)';
            };

            image.onerror = function() {
                console.error('Failed to load colored image:', coloredImageSrc);
                this.src = 'images/placeholder.jpg';
            };

            enhancedSpeak(`ممتاز! هذا هو اللون ${selectedColorArabic} الصحيح!`, 'ar', 'excellent.mp3');
            celebrateCorrectAnswer();

            // تعطيل التفاعل
            image.style.pointerEvents = 'none';
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.6';
            });

            setTimeout(() => {
                currentShapeIndex++;
                showColoringGame();
            }, 3000);

        } else {
            // إجابة خاطئة
            image.style.animation = 'shake 0.6s ease-in-out';
            enhancedSpeak(`هذا ليس اللون الصحيح، حاول مرة أخرى`, 'ar', 'try_again.mp3');

            // إعادة تعيين اللون المختار
            selectedColor = null;
            selectedColorHex = null;
            selectedColorArabic = null;

            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.classList.remove('selected');
            });

            setTimeout(() => {
                image.style.animation = '';
            }, 600);
        }
    }

    function completeColoringGame() {
        enhancedSpeak('رائع! أكملت جميع الأشكال!', 'ar', 'wonderful.wav');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showColoringGame();
}

// لعبة ترتيب الأرقام الجديدة
function loadNumberSequenceGame() {
    let placedNumbers = 0;
    const totalNumbers = 10;
    const numbers = Array.from({length: totalNumbers}, (_, i) => i + 1);
    const shuffledNumbers = shuffleArray([...numbers]);

    function showSequenceGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="sequence-game">
                <h2>🔢 لعبة ترتيب الأرقام</h2>
                <p class="game-instruction">اسحب الأرقام ورتبها من 1 إلى 10</p>
                <p class="game-instruction-en">Drag the numbers and arrange them from 1 to 10</p>

                <div class="sequence-container">
                    <div class="numbers-pool">
                        <h3>الأرقام</h3>
                        <div class="draggable-numbers">
                            ${shuffledNumbers.map(num => `
                                <div class="draggable-number"
                                     draggable="true"
                                     data-number="${num}"
                                     id="number-${num}">
                                    <span class="number-value">${num}</span>
                                    <span class="number-word">${numbersData[num-1].word}</span>
                                    <button class="number-speak-btn" onclick="enhancedSpeak('${numbersData[num-1].arabic}', 'ar', '${num}.mp3')">🔊</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="sequence-line">
                        <h3>رتب الأرقام هنا</h3>
                        <div class="sequence-slots">
                            ${numbers.map(num => `
                                <div class="sequence-slot"
                                     data-position="${num}"
                                     id="slot-${num}">
                                    <div class="slot-number">${num}</div>
                                    <div class="slot-indicator">ضع الرقم ${num}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="sequence-progress">
                    <div class="progress-text">الأرقام المرتبة: <span id="placed-count">0</span> من ${totalNumbers}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="sequence-progress-fill" style="width: 0%"></div>
                    </div>
                    <button class="check-sequence-btn" onclick="checkSequence()" disabled>تحقق من الترتيب</button>
                </div>
            </div>
        `;

        setupSequenceDragAndDrop();
    }

    function setupSequenceDragAndDrop() {
        const draggableNumbers = document.querySelectorAll('.draggable-number');
        const sequenceSlots = document.querySelectorAll('.sequence-slot');

        // إعداد الأرقام القابلة للسحب
        draggableNumbers.forEach(number => {
            number.addEventListener('dragstart', handleSequenceDragStart);
            number.addEventListener('dragend', handleSequenceDragEnd);

            // دعم اللمس للأجهزة المحمولة
            number.addEventListener('touchstart', handleSequenceTouchStart, { passive: false });
            number.addEventListener('touchmove', handleSequenceTouchMove, { passive: false });
            number.addEventListener('touchend', handleSequenceTouchEnd, { passive: false });
        });

        // إعداد مواضع الترتيب
        sequenceSlots.forEach(slot => {
            slot.addEventListener('dragover', handleSequenceDragOver);
            slot.addEventListener('drop', handleSequenceDrop);
            slot.addEventListener('dragenter', handleSequenceDragEnter);
            slot.addEventListener('dragleave', handleSequenceDragLeave);
        });
    }

    let draggedNumber = null;
    let touchOffset = { x: 0, y: 0 };

    function handleSequenceDragStart(e) {
        draggedNumber = e.target;
        e.target.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.target.outerHTML);
    }

    function handleSequenceDragEnd(e) {
        e.target.classList.remove('dragging');
        draggedNumber = null;
    }

    function handleSequenceDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    }

    function handleSequenceDragEnter(e) {
        e.preventDefault();
        e.target.closest('.sequence-slot').classList.add('drag-over');
    }

    function handleSequenceDragLeave(e) {
        if (!e.target.closest('.sequence-slot').contains(e.relatedTarget)) {
            e.target.closest('.sequence-slot').classList.remove('drag-over');
        }
    }

    function handleSequenceDrop(e) {
        e.preventDefault();
        const slot = e.target.closest('.sequence-slot');
        slot.classList.remove('drag-over');

        if (draggedNumber) {
            placeNumberInSlot(draggedNumber, slot);
        }
    }

    // دعم اللمس للأجهزة المحمولة
    function handleSequenceTouchStart(e) {
        e.preventDefault();
        draggedNumber = e.target;
        const touch = e.touches[0];
        const rect = draggedNumber.getBoundingClientRect();
        touchOffset.x = touch.clientX - rect.left;
        touchOffset.y = touch.clientY - rect.top;

        draggedNumber.classList.add('dragging');
        draggedNumber.style.position = 'fixed';
        draggedNumber.style.zIndex = '1000';
        draggedNumber.style.pointerEvents = 'none';
    }

    function handleSequenceTouchMove(e) {
        e.preventDefault();
        if (!draggedNumber) return;

        const touch = e.touches[0];
        draggedNumber.style.left = (touch.clientX - touchOffset.x) + 'px';
        draggedNumber.style.top = (touch.clientY - touchOffset.y) + 'px';
    }

    function handleSequenceTouchEnd(e) {
        e.preventDefault();
        if (!draggedNumber) return;

        const touch = e.changedTouches[0];
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
        const slot = elementBelow ? elementBelow.closest('.sequence-slot') : null;

        // إعادة تعيين الموضع
        draggedNumber.style.position = '';
        draggedNumber.style.zIndex = '';
        draggedNumber.style.pointerEvents = '';
        draggedNumber.style.left = '';
        draggedNumber.style.top = '';
        draggedNumber.classList.remove('dragging');

        if (slot) {
            placeNumberInSlot(draggedNumber, slot);
        }

        draggedNumber = null;
    }

    function placeNumberInSlot(numberElement, slot) {
        const numberValue = parseInt(numberElement.dataset.number);
        const slotPosition = parseInt(slot.dataset.position);

        // التحقق من وجود رقم في الموضع
        const existingNumber = slot.querySelector('.placed-number');
        if (existingNumber) {
            // إرجاع الرقم الموجود إلى المجموعة
            returnNumberToPool(existingNumber);
        }

        // وضع الرقم الجديد
        const placedNumber = numberElement.cloneNode(true);
        placedNumber.classList.add('placed-number');
        placedNumber.classList.remove('dragging');
        placedNumber.draggable = false;

        // إخفاء مؤشر الموضع
        const slotIndicator = slot.querySelector('.slot-indicator');
        slotIndicator.style.display = 'none';

        slot.appendChild(placedNumber);

        // إخفاء الرقم الأصلي
        numberElement.style.display = 'none';

        // تحديث التقدم
        updateSequenceProgress();

        // تأثيرات بصرية
        if (numberValue === slotPosition) {
            slot.classList.add('correct-position');
            enhancedSpeak('موضع صحيح!', 'ar', 'correct.mp3');
        } else {
            slot.classList.add('incorrect-position');
        }
    }

    function returnNumberToPool(placedNumber) {
        const numberValue = placedNumber.dataset.number;
        const originalNumber = document.getElementById(`number-${numberValue}`);
        if (originalNumber) {
            originalNumber.style.display = 'block';
        }

        // إزالة الرقم من الموضع
        const slot = placedNumber.closest('.sequence-slot');
        const slotIndicator = slot.querySelector('.slot-indicator');
        slotIndicator.style.display = 'block';

        slot.classList.remove('correct-position', 'incorrect-position');
        placedNumber.remove();

        updateSequenceProgress();
    }

    function updateSequenceProgress() {
        const placedNumbers = document.querySelectorAll('.placed-number').length;
        const placedCount = document.getElementById('placed-count');
        const progressFill = document.getElementById('sequence-progress-fill');
        const checkBtn = document.querySelector('.check-sequence-btn');

        if (placedCount) {
            placedCount.textContent = placedNumbers;
        }

        if (progressFill) {
            const percentage = (placedNumbers / totalNumbers) * 100;
            progressFill.style.width = percentage + '%';
        }

        if (checkBtn) {
            checkBtn.disabled = placedNumbers < totalNumbers;
        }
    }

    window.checkSequence = function() {
        let correctCount = 0;
        const slots = document.querySelectorAll('.sequence-slot');

        slots.forEach(slot => {
            const placedNumber = slot.querySelector('.placed-number');
            if (placedNumber) {
                const numberValue = parseInt(placedNumber.dataset.number);
                const slotPosition = parseInt(slot.dataset.position);

                if (numberValue === slotPosition) {
                    correctCount++;
                    slot.classList.add('final-correct');
                    recordAnswer(true);
                } else {
                    slot.classList.add('final-incorrect');
                    recordAnswer(false);
                }
            }
        });

        if (correctCount === totalNumbers) {
            // ترتيب صحيح كامل
            enhancedSpeak('ممتاز! رتبت جميع الأرقام بشكل صحيح!', 'ar', 'excellent.mp3');
            celebrateGameCompletion();

            setTimeout(() => {
                nextGame();
            }, 3000);
        } else {
            // ترتيب غير مكتمل
            enhancedSpeak(`أحسنت! ${correctCount} أرقام في المكان الصحيح من ${totalNumbers}`, 'ar', 'good.mp3');

            setTimeout(() => {
                // إعادة تعيين الأرقام الخاطئة
                resetIncorrectNumbers();
            }, 2000);
        }
    };

    function resetIncorrectNumbers() {
        const incorrectSlots = document.querySelectorAll('.final-incorrect');

        incorrectSlots.forEach(slot => {
            const placedNumber = slot.querySelector('.placed-number');
            if (placedNumber) {
                returnNumberToPool(placedNumber);
            }
            slot.classList.remove('final-incorrect');
        });

        // إزالة تأثيرات الأرقام الصحيحة
        document.querySelectorAll('.final-correct').forEach(slot => {
            slot.classList.remove('final-correct');
        });

        enhancedSpeak('حاول ترتيب الأرقام المتبقية', 'ar', 'try_again.mp3');
    }

    showSequenceGame();
}

// لعبة الذاكرة الجديدة - الحيوانات
function loadMemoryGame() {
    let flippedCards = [];
    let matchedPairs = 0;
    let moves = 0;
    let gameStarted = false; // حالة اللعبة
    const totalPairs = 6; // 6 أزواج من الحيوانات
    const gameAnimals = shuffleArray(animalsData).slice(0, totalPairs);

    // إنشاء أزواج البطاقات
    const cards = [];
    gameAnimals.forEach((animal, index) => {
        // بطاقة الصورة
        cards.push({
            id: `img-${index}`,
            type: 'image',
            animal: animal,
            matched: false,
            flipped: false
        });
        // بطاقة الكلمة
        cards.push({
            id: `word-${index}`,
            type: 'word',
            animal: animal,
            matched: false,
            flipped: false
        });
    });

    // خلط البطاقات
    const shuffledCards = shuffleArray(cards);

    function showMemoryGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="memory-game">
                <h2>🧠 لعبة الذاكرة - الحيوانات</h2>
                <p class="game-instruction" id="memory-instruction">احفظ مواقع البطاقات ثم انقر "مستعد للعب"</p>
                <p class="game-instruction-en" id="memory-instruction-en">Memorize the card positions then click "Ready to Play"</p>

                <div class="memory-stats">
                    <div class="stat-item">
                        <span class="stat-label">الحركات:</span>
                        <span class="stat-value" id="moves-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">الأزواج:</span>
                        <span class="stat-value" id="pairs-count">0</span>
                        <span class="stat-total">/ ${totalPairs}</span>
                    </div>
                </div>

                <div class="memory-board">
                    ${shuffledCards.map(card => `
                        <div class="memory-card"
                             data-card-id="${card.id}"
                             data-animal-word="${card.animal.word}"
                             onclick="flipCard('${card.id}')">
                            <div class="card-inner">
                                <div class="card-front">
                                    <div class="card-question">?</div>
                                </div>
                                <div class="card-back">
                                    ${card.type === 'image' ?
                                        `<div class="card-image">
                                            <div class="card-emoji">${card.animal.emoji}</div>
                                            <img src="${card.animal.image}" alt="${card.animal.word}"
                                                 onerror="this.style.display='none'">
                                        </div>` :
                                        `<div class="card-word">
                                            <div class="word-en">${card.animal.word}</div>
                                            <div class="word-ar">${card.animal.arabic}</div>
                                            <button class="card-speak-btn" onclick="event.stopPropagation(); enhancedSpeak('${card.animal.arabic}', 'ar', '${card.animal.word.toLowerCase()}_ar.mp3')">🔊</button>
                                        </div>`
                                    }
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div class="memory-controls">
                    <button id="start-game-btn" class="start-game-btn" onclick="startMemoryGame()">
                        🎮 مستعد للعب!
                    </button>
                </div>

                <div class="memory-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="memory-progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">التقدم: ${matchedPairs} من ${totalPairs} أزواج</div>
                </div>
            </div>
        `;

        // إظهار جميع البطاقات في البداية
        setTimeout(() => {
            showAllCards();
        }, 500);
    }

    function showAllCards() {
        // إظهار جميع البطاقات للحفظ
        shuffledCards.forEach(card => {
            const cardElement = document.querySelector(`[data-card-id="${card.id}"]`);
            if (cardElement) {
                cardElement.classList.add('flipped');
            }
        });
    }

    window.startMemoryGame = function() {
        gameStarted = true;

        // إخفاء جميع البطاقات
        shuffledCards.forEach(card => {
            const cardElement = document.querySelector(`[data-card-id="${card.id}"]`);
            if (cardElement) {
                cardElement.classList.remove('flipped');
            }
        });

        // تحديث التعليمات
        document.getElementById('memory-instruction').textContent = 'اقلب البطاقات للعثور على أزواج متطابقة (صورة + كلمة)';
        document.getElementById('memory-instruction-en').textContent = 'Flip cards to find matching pairs (image + word)';

        // إخفاء زر البدء
        document.getElementById('start-game-btn').style.display = 'none';

        enhancedSpeak('ابدأ اللعب! ابحث عن الأزواج المتطابقة', 'ar', 'start_game.wav');
    };

    window.flipCard = function(cardId) {
        // لا تسمح بقلب البطاقات قبل بدء اللعبة
        if (!gameStarted) {
            enhancedSpeak('انقر على زر مستعد للعب أولاً', 'ar', 'try_again.wav');
            return;
        }

        const cardElement = document.querySelector(`[data-card-id="${cardId}"]`);
        const card = shuffledCards.find(c => c.id === cardId);

        // تجاهل النقر إذا كانت البطاقة مقلوبة أو متطابقة
        if (card.flipped || card.matched || flippedCards.length >= 2) {
            return;
        }

        // قلب البطاقة
        card.flipped = true;
        cardElement.classList.add('flipped');
        flippedCards.push(card);

        // تشغيل صوت القلب
        enhancedSpeak('', 'ar', 'click.mp3');

        // التحقق من المطابقة عند قلب بطاقتين
        if (flippedCards.length === 2) {
            moves++;
            updateStats();

            setTimeout(() => {
                checkMatch();
            }, 1000);
        }
    };

    function checkMatch() {
        const [card1, card2] = flippedCards;
        const isMatch = card1.animal.word === card2.animal.word && card1.type !== card2.type;

        if (isMatch) {
            // مطابقة صحيحة
            card1.matched = true;
            card2.matched = true;

            const card1Element = document.querySelector(`[data-card-id="${card1.id}"]`);
            const card2Element = document.querySelector(`[data-card-id="${card2.id}"]`);

            card1Element.classList.add('matched');
            card2Element.classList.add('matched');

            matchedPairs++;
            recordAnswer(true);
            updateStats();

            enhancedSpeak('ممتاز! مطابقة صحيحة!', 'ar', 'excellent.mp3');
            celebrateCorrectAnswer();

            // التحقق من اكتمال اللعبة
            if (matchedPairs >= totalPairs) {
                setTimeout(() => {
                    completeMemoryGame();
                }, 1500);
            }
        } else {
            // مطابقة خاطئة
            recordAnswer(false);
            enhancedSpeak('حاول مرة أخرى', 'ar', 'try_again.mp3');

            setTimeout(() => {
                // إعادة قلب البطاقات
                card1.flipped = false;
                card2.flipped = false;

                const card1Element = document.querySelector(`[data-card-id="${card1.id}"]`);
                const card2Element = document.querySelector(`[data-card-id="${card2.id}"]`);

                card1Element.classList.remove('flipped');
                card2Element.classList.remove('flipped');
            }, 500);
        }

        // إعادة تعيين البطاقات المقلوبة
        flippedCards = [];
    }

    function updateStats() {
        const movesCount = document.getElementById('moves-count');
        const pairsCount = document.getElementById('pairs-count');
        const progressFill = document.getElementById('memory-progress-fill');

        if (movesCount) {
            movesCount.textContent = moves;
        }

        if (pairsCount) {
            pairsCount.textContent = matchedPairs;
        }

        if (progressFill) {
            const percentage = (matchedPairs / totalPairs) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeMemoryGame() {
        enhancedSpeak(`رائع! أكملت اللعبة في ${moves} حركة!`, 'ar', 'wonderful.mp3');
        celebrateGameCompletion();

        // إضافة تأثير النجاح لجميع البطاقات
        document.querySelectorAll('.memory-card').forEach(card => {
            card.classList.add('game-complete');
        });

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showMemoryGame();
}

// لعبة "أين الجزء؟" الجديدة - أجزاء الجسم
function loadBodyPartsPointingGame() {
    let currentQuestionIndex = 0;

    // بيانات أجزاء الجسم مع الأرقام
    const bodyPartsWithNumbers = [
        { word: 'Head', arabic: 'رأس', number: 1, emoji: '👤' },
        { word: 'Eye', arabic: 'عين', number: 2, emoji: '👁️' },
        { word: 'Nose', arabic: 'أنف', number: 3, emoji: '👃' },
        { word: 'Mouth', arabic: 'فم', number: 4, emoji: '👄' },
        { word: 'Ear', arabic: 'أذن', number: 5, emoji: '👂' },
        { word: 'Hand', arabic: 'يد', number: 6, emoji: '✋' },
        { word: 'Foot', arabic: 'قدم', number: 7, emoji: '🦶' }
    ];

    const questions = shuffleArray(bodyPartsWithNumbers).slice(0, 5);

    function showPointingGame() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }

        const currentBodyPart = questions[currentQuestionIndex];

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="body-parts-pointing-game">
                <h2>👤 لعبة أين الجزء؟</h2>
                <p class="game-instruction">اختر رقم ${currentBodyPart.arabic}</p>
                <p class="game-instruction-en">Choose the number for ${currentBodyPart.word}</p>

                <div class="pointing-container">
                    <div class="question-info">
                        <div class="target-part">
                            <div class="part-emoji">${currentBodyPart.emoji}</div>
                            <div class="part-names">
                                <span class="part-ar">${currentBodyPart.arabic}</span>
                                <span class="part-en">${currentBodyPart.word}</span>
                            </div>
                            <button class="part-speak-btn" onclick="enhancedSpeak('${currentBodyPart.arabic}', 'ar', '${currentBodyPart.word.toLowerCase()}_ar.wav')">🔊</button>
                        </div>
                    </div>

                    <div class="body-diagram">
                        <img src="images/body_parts_numbered.png" alt="جسم الإنسان" class="body-image">

                        <!-- مؤشرات المساعدة -->
                        <div class="help-hints">
                            <div class="hint">💡 انظر للصورة واختر الرقم الصحيح</div>
                        </div>
                    </div>

                    <div class="number-buttons">
                        <h3>اختر الرقم:</h3>
                        <div class="numbers-grid">
                            ${bodyPartsWithNumbers.map(part => `
                                <button class="number-btn"
                                        data-number="${part.number}"
                                        onclick="checkBodyPartNumber(${part.number})">
                                    ${part.number}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="pointing-progress">
                    <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${(currentQuestionIndex / questions.length) * 100}%"></div>
                    </div>
                </div>
            </div>
        `;

    }

    window.checkBodyPartNumber = function(selectedNumber) {
        const currentBodyPart = questions[currentQuestionIndex];
        const isCorrect = selectedNumber === currentBodyPart.number;

        recordAnswer(isCorrect);

        // تمييز الزر المختار
        const selectedBtn = document.querySelector(`[data-number="${selectedNumber}"]`);
        const correctBtn = document.querySelector(`[data-number="${currentBodyPart.number}"]`);

        if (isCorrect) {
            // إجابة صحيحة
            selectedBtn.style.background = '#4CAF50';
            selectedBtn.style.color = 'white';
            selectedBtn.style.transform = 'scale(1.1)';

            enhancedSpeak(`ممتاز! هذا هو رقم ${currentBodyPart.arabic}`, 'ar', 'excellent.wav');
            celebrateCorrectAnswer();

            // تعطيل جميع الأزرار
            document.querySelectorAll('.number-btn').forEach(btn => {
                btn.disabled = true;
            });

            setTimeout(() => {
                currentQuestionIndex++;
                showPointingGame();
            }, 2500);

        } else {
            // إجابة خاطئة
            selectedBtn.style.background = '#f44336';
            selectedBtn.style.color = 'white';
            selectedBtn.style.animation = 'shake 0.6s ease-in-out';

            enhancedSpeak(`هذا ليس رقم ${currentBodyPart.arabic}، حاول مرة أخرى`, 'ar', 'try_again.wav');

            // إعادة تعيين اللون بعد ثانية
            setTimeout(() => {
                selectedBtn.style.background = '';
                selectedBtn.style.color = '';
                selectedBtn.style.animation = '';
            }, 1000);

            // إظهار تلميح للرقم الصحيح
            setTimeout(() => {
                correctBtn.style.animation = 'pulse 1s ease-in-out 3';
                setTimeout(() => {
                    correctBtn.style.animation = '';
                }, 3000);
            }, 1500);
        }
    };

    showPointingGame();
}

// لعبة التصنيف الجديدة - الطعام والشراب
function loadCategorizationGame() {
    let categorizedItems = 0;
    const totalItems = foodData.length;
    const shuffledFood = shuffleArray([...foodData]);

    function showCategorizationGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="categorization-game">
                <h2>📂 لعبة التصنيف - الطعام والشراب</h2>
                <p class="game-instruction">اسحب كل عنصر إلى الفئة المناسبة</p>
                <p class="game-instruction-en">Drag each item to the correct category</p>

                <div class="categorization-container">
                    <div class="items-pool">
                        <h3>العناصر</h3>
                        <div class="draggable-items">
                            ${shuffledFood.map((item, index) => `
                                <div class="draggable-item"
                                     draggable="true"
                                     data-category="${item.category}"
                                     data-word="${item.word}"
                                     id="item-${index}">
                                    <div class="item-emoji">${item.emoji}</div>
                                    <div class="item-info">
                                        <span class="item-en">${item.word}</span>
                                        <span class="item-ar">${item.arabic}</span>
                                    </div>
                                    <button class="item-speak-btn" onclick="enhancedSpeak('${item.arabic}', 'ar', '${item.word.toLowerCase()}_ar.mp3')">🔊</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="categories-area">
                        <h3>الفئات</h3>
                        <div class="category-containers">
                            ${categories.map(category => `
                                <div class="category-container"
                                     data-category="${category.id}"
                                     style="border-color: ${category.color}">
                                    <div class="category-header" style="background-color: ${category.color}">
                                        <div class="category-emoji">${category.emoji}</div>
                                        <div class="category-info">
                                            <span class="category-name">${category.name}</span>
                                            <span class="category-name-en">${category.nameEn}</span>
                                        </div>
                                        <button class="category-speak-btn" onclick="enhancedSpeak('${category.name}', 'ar')">🔊</button>
                                    </div>
                                    <div class="category-drop-zone" data-category="${category.id}">
                                        <div class="drop-hint">${category.description}</div>
                                        <div class="category-items" id="category-${category.id}"></div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="categorization-progress">
                    <div class="progress-text">العناصر المصنفة: <span id="categorized-count">0</span> من ${totalItems}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="categorization-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupCategorizationDragAndDrop();
    }

    function setupCategorizationDragAndDrop() {
        const draggableItems = document.querySelectorAll('.draggable-item');
        const dropZones = document.querySelectorAll('.category-drop-zone');

        // إعداد العناصر القابلة للسحب
        draggableItems.forEach(item => {
            item.addEventListener('dragstart', handleCategorizationDragStart);
            item.addEventListener('dragend', handleCategorizationDragEnd);

            // دعم اللمس للأجهزة المحمولة
            item.addEventListener('touchstart', handleCategorizationTouchStart, { passive: false });
            item.addEventListener('touchmove', handleCategorizationTouchMove, { passive: false });
            item.addEventListener('touchend', handleCategorizationTouchEnd, { passive: false });
        });

        // إعداد مناطق الإفلات
        dropZones.forEach(zone => {
            zone.addEventListener('dragover', handleCategorizationDragOver);
            zone.addEventListener('drop', handleCategorizationDrop);
            zone.addEventListener('dragenter', handleCategorizationDragEnter);
            zone.addEventListener('dragleave', handleCategorizationDragLeave);
        });
    }

    let draggedItem = null;
    let touchOffset = { x: 0, y: 0 };

    function handleCategorizationDragStart(e) {
        draggedItem = e.target;
        e.target.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.target.outerHTML);
    }

    function handleCategorizationDragEnd(e) {
        e.target.classList.remove('dragging');
        draggedItem = null;
    }

    function handleCategorizationDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    }

    function handleCategorizationDragEnter(e) {
        e.preventDefault();
        e.target.closest('.category-drop-zone').classList.add('drag-over');
    }

    function handleCategorizationDragLeave(e) {
        if (!e.target.closest('.category-drop-zone').contains(e.relatedTarget)) {
            e.target.closest('.category-drop-zone').classList.remove('drag-over');
        }
    }

    function handleCategorizationDrop(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.category-drop-zone');
        dropZone.classList.remove('drag-over');

        if (draggedItem) {
            checkCategorization(draggedItem, dropZone);
        }
    }

    // دعم اللمس للأجهزة المحمولة
    function handleCategorizationTouchStart(e) {
        e.preventDefault();
        draggedItem = e.target;
        const touch = e.touches[0];
        const rect = draggedItem.getBoundingClientRect();
        touchOffset.x = touch.clientX - rect.left;
        touchOffset.y = touch.clientY - rect.top;

        draggedItem.classList.add('dragging');
        draggedItem.style.position = 'fixed';
        draggedItem.style.zIndex = '1000';
        draggedItem.style.pointerEvents = 'none';
    }

    function handleCategorizationTouchMove(e) {
        e.preventDefault();
        if (!draggedItem) return;

        const touch = e.touches[0];
        draggedItem.style.left = (touch.clientX - touchOffset.x) + 'px';
        draggedItem.style.top = (touch.clientY - touchOffset.y) + 'px';
    }

    function handleCategorizationTouchEnd(e) {
        e.preventDefault();
        if (!draggedItem) return;

        const touch = e.changedTouches[0];
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
        const dropZone = elementBelow ? elementBelow.closest('.category-drop-zone') : null;

        // إعادة تعيين الموضع
        draggedItem.style.position = '';
        draggedItem.style.zIndex = '';
        draggedItem.style.pointerEvents = '';
        draggedItem.style.left = '';
        draggedItem.style.top = '';
        draggedItem.classList.remove('dragging');

        if (dropZone) {
            checkCategorization(draggedItem, dropZone);
        }

        draggedItem = null;
    }

    function checkCategorization(itemElement, dropZone) {
        const itemCategory = itemElement.dataset.category;
        const targetCategory = dropZone.dataset.category;
        const isCorrect = itemCategory === targetCategory;

        if (isCorrect) {
            // تصنيف صحيح
            handleCorrectCategorization(itemElement, dropZone);
        } else {
            // تصنيف خاطئ
            handleIncorrectCategorization(itemElement, targetCategory);
        }
    }

    function handleCorrectCategorization(itemElement, dropZone) {
        // إخفاء تلميح الإفلات
        const dropHint = dropZone.querySelector('.drop-hint');
        if (dropHint && dropZone.querySelectorAll('.categorized-item').length === 0) {
            dropHint.style.display = 'none';
        }

        // إضافة العنصر إلى الفئة
        const categoryItems = dropZone.querySelector('.category-items');
        const categorizedItem = itemElement.cloneNode(true);
        categorizedItem.classList.add('categorized-item');
        categorizedItem.classList.remove('dragging');
        categorizedItem.draggable = false;

        // إزالة أزرار النطق من العنصر المصنف
        const speakBtn = categorizedItem.querySelector('.item-speak-btn');
        if (speakBtn) speakBtn.remove();

        categoryItems.appendChild(categorizedItem);

        // إخفاء العنصر الأصلي
        itemElement.style.display = 'none';

        // تسجيل النقاط والتقدم
        categorizedItems++;
        recordAnswer(true);
        updateCategorizationProgress();

        // تأثيرات بصرية وصوتية
        dropZone.classList.add('correct-categorization');
        const categoryName = categories.find(cat => cat.id === dropZone.dataset.category).name;
        enhancedSpeak(`ممتاز! هذا ينتمي إلى ${categoryName}`, 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        setTimeout(() => {
            dropZone.classList.remove('correct-categorization');
        }, 1000);

        // التحقق من اكتمال اللعبة
        if (categorizedItems >= totalItems) {
            setTimeout(() => {
                completeCategorizationGame();
            }, 1500);
        }
    }

    function handleIncorrectCategorization(itemElement, targetCategory) {
        const targetCategoryName = categories.find(cat => cat.id === targetCategory).name;

        // تأثير اهتزاز للعنصر
        itemElement.classList.add('shake-animation');
        enhancedSpeak(`هذا لا ينتمي إلى ${targetCategoryName}، جرب فئة أخرى`, 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            itemElement.classList.remove('shake-animation');
        }, 600);
    }

    function updateCategorizationProgress() {
        const categorizedCount = document.getElementById('categorized-count');
        const progressFill = document.getElementById('categorization-progress-fill');

        if (categorizedCount) {
            categorizedCount.textContent = categorizedItems;
        }

        if (progressFill) {
            const percentage = (categorizedItems / totalItems) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeCategorizationGame() {
        enhancedSpeak('رائع! صنفت جميع العناصر بشكل صحيح!', 'ar', 'wonderful.mp3');
        celebrateGameCompletion();

        // تأثير النجاح لجميع الفئات
        document.querySelectorAll('.category-container').forEach(container => {
            container.classList.add('game-complete');
        });

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showCategorizationGame();
}
