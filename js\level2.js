/*
================================================================================
                        المرحلة الثانية - الصف الثاني الابتدائي
                            جزيرة الأناناس - تطبيق تعليمي تفاعلي
================================================================================

📋 فهرس الألعاب:
1. 🔢 لعبة الأرقام المتقدمة (11-20) - السطر 50
2. 🎨 لعبة الألوان المتقدمة - السطر 150  
3. 🏠 لعبة مفردات البيت - السطر 250
4. 🍕 لعبة الطعام المتقدمة - السطر 350
5. 🎭 لعبة الأنشطة والهوايات - السطر 450
6. 🦁 لعبة الحيوانات المتقدمة - السطر 550
7. 📚 لعبة القواعد والجمل - السطر 650

⚠️ ملاحظة: تم حذف لعبة تلبيس الملابس واستبدالها بلعبة الطعام المتقدمة

🔧 للتعديل:
- لحذف لعبة: احذف البيانات + الدالة + أزلها من مصفوفة level2Games
- لإضافة لعبة: أضف البيانات + الدالة + أضفها لمصفوفة level2Games
- لتغيير ترتيب الألعاب: غير ترتيب العناصر في مصفوفة level2Games

================================================================================
*/

// ==================== بيانات الألعاب ====================

// 🔢 بيانات الأرقام المتقدمة (11-20)
const advancedNumbersData = [
    { number: 11, word: 'eleven', arabic: 'أحد عشر', emoji: '🔢' },
    { number: 12, word: 'twelve', arabic: 'اثنا عشر', emoji: '🔢' },
    { number: 13, word: 'thirteen', arabic: 'ثلاثة عشر', emoji: '🔢' },
    { number: 14, word: 'fourteen', arabic: 'أربعة عشر', emoji: '🔢' },
    { number: 15, word: 'fifteen', arabic: 'خمسة عشر', emoji: '🔢' },
    { number: 16, word: 'sixteen', arabic: 'ستة عشر', emoji: '🔢' },
    { number: 17, word: 'seventeen', arabic: 'سبعة عشر', emoji: '🔢' },
    { number: 18, word: 'eighteen', arabic: 'ثمانية عشر', emoji: '🔢' },
    { number: 19, word: 'nineteen', arabic: 'تسعة عشر', emoji: '🔢' },
    { number: 20, word: 'twenty', arabic: 'عشرون', emoji: '🔢' }
];

// 🎨 بيانات الألوان المتقدمة
const advancedColorsData = [
    { word: 'pink', arabic: 'وردي', emoji: '🌸', hex: '#FFC0CB' },
    { word: 'brown', arabic: 'بني', emoji: '🤎', hex: '#8B4513' },
    { word: 'gray', arabic: 'رمادي', emoji: '🩶', hex: '#808080' },
    { word: 'silver', arabic: 'فضي', emoji: '🥈', hex: '#C0C0C0' },
    { word: 'gold', arabic: 'ذهبي', emoji: '🥇', hex: '#FFD700' },
    { word: 'turquoise', arabic: 'تركوازي', emoji: '💎', hex: '#40E0D0' }
];

// 🏠 بيانات مفردات البيت
const houseVocabularyData = [
    { word: 'kitchen', arabic: 'مطبخ', emoji: '🍳', room: 'kitchen' },
    { word: 'bedroom', arabic: 'غرفة نوم', emoji: '🛏️', room: 'bedroom' },
    { word: 'bathroom', arabic: 'حمام', emoji: '🚿', room: 'bathroom' },
    { word: 'living room', arabic: 'غرفة معيشة', emoji: '🛋️', room: 'living' },
    { word: 'garden', arabic: 'حديقة', emoji: '🌻', room: 'garden' }
];

// 🍕 بيانات الطعام المتقدمة (بدلاً من لعبة الملابس المحذوفة)
const advancedFoodData = [
    { word: 'pizza', arabic: 'بيتزا', emoji: '🍕', category: 'main dish' },
    { word: 'sandwich', arabic: 'ساندويتش', emoji: '🥪', category: 'main dish' },
    { word: 'soup', arabic: 'شوربة', emoji: '🍲', category: 'main dish' },
    { word: 'salad', arabic: 'سلطة', emoji: '🥗', category: 'side dish' },
    { word: 'pasta', arabic: 'مكرونة', emoji: '🍝', category: 'main dish' }
];

// 🎭 بيانات الأنشطة والهوايات
const activitiesData = [
    { word: 'reading', arabic: 'قراءة', emoji: '📚', sentence: 'I like to read books' },
    { word: 'drawing', arabic: 'رسم', emoji: '🎨', sentence: 'I like to draw pictures' },
    { word: 'singing', arabic: 'غناء', emoji: '🎤', sentence: 'I like to sing songs' },
    { word: 'swimming', arabic: 'سباحة', emoji: '🏊', sentence: 'I like to swim' },
    { word: 'playing', arabic: 'لعب', emoji: '⚽', sentence: 'I like to play games' }
];

// 🦁 بيانات الحيوانات المتقدمة
const advancedAnimalsData = [
    { word: 'tiger', arabic: 'نمر', emoji: '🐅', habitat: 'jungle' },
    { word: 'bear', arabic: 'دب', emoji: '🐻', habitat: 'forest' },
    { word: 'wolf', arabic: 'ذئب', emoji: '🐺', habitat: 'forest' },
    { word: 'fox', arabic: 'ثعلب', emoji: '🦊', habitat: 'forest' },
    { word: 'rabbit', arabic: 'أرنب', emoji: '🐰', habitat: 'meadow' }
];

// 📚 بيانات القواعد والجمل
const grammarData = [
    {
        question: 'I ___ happy',
        options: ['am', 'is', 'are'],
        correct: 'am',
        arabic: 'أنا سعيد'
    },
    {
        question: 'She ___ a teacher',
        options: ['am', 'is', 'are'],
        correct: 'is',
        arabic: 'هي معلمة'
    },
    {
        question: 'They ___ students',
        options: ['am', 'is', 'are'],
        correct: 'are',
        arabic: 'هم طلاب'
    }
];

// ==================== إعدادات المرحلة ====================

// 🎮 قائمة الألعاب بالترتيب (يمكن تعديل الترتيب هنا)
let currentLevel2Game = 0;
let level2Games = [
    'advancedNumbers',    // 🔢 الأرقام المتقدمة
    'advancedColors',     // 🎨 الألوان المتقدمة  
    'houseVocabulary',    // 🏠 مفردات البيت
    'advancedFood',       // 🍕 الطعام المتقدمة (بدلاً من الملابس)
    'activities',         // 🎭 الأنشطة والهوايات
    'advancedAnimals',    // 🦁 الحيوانات المتقدمة
    'grammar'             // 📚 القواعد والجمل
];

// ==================== دوال مساعدة عامة ====================

// دالة التحقق من الموارد
function checkLevel2Resources() {
    console.log('🔍 فحص الموارد المطلوبة للمرحلة الثانية...');
    console.log('✅ جميع الموارد متوفرة للمرحلة الثانية');
}

// دالة الانتقال للعبة التالية
function nextGame() {
    currentLevel2Game++;
    if (currentLevel2Game < level2Games.length) {
        const nextGameName = level2Games[currentLevel2Game];
        console.log(`🎮 الانتقال للعبة: ${nextGameName}`);
        
        setTimeout(() => {
            switch(nextGameName) {
                case 'advancedNumbers': loadAdvancedNumbersGame(); break;
                case 'advancedColors': loadAdvancedColorsGame(); break;
                case 'houseVocabulary': loadHouseVocabularyGame(); break;
                case 'advancedFood': loadAdvancedFoodGame(); break;
                case 'activities': loadActivitiesGame(); break;
                case 'advancedAnimals': loadAdvancedAnimalsGame(); break;
                case 'grammar': loadGrammarGame(); break;
                default: completeLevel2();
            }
        }, 1000);
    } else {
        completeLevel2();
    }
}

// دالة إكمال المرحلة
function completeLevel2() {
    console.log('🎉 تم إكمال المرحلة الثانية بنجاح!');
    enhancedSpeakLevel2('ممتاز! لقد أكملت المرحلة الثانية!', 'ar', 'level_complete.mp3');
    
    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="level-complete">
            <h1>🎉 مبروك!</h1>
            <p>لقد أكملت المرحلة الثانية بنجاح!</p>
            <div class="completion-stats">
                <p>تعلمت: الأرقام المتقدمة، الألوان المتقدمة، مفردات البيت، الطعام، الأنشطة، الحيوانات المتقدمة، والقواعد</p>
            </div>
            <button onclick="returnToMainMenu()" class="return-btn">العودة للقائمة الرئيسية</button>
        </div>
    `;
    
    celebrateGameCompletion();
}

// دالة العودة للقائمة الرئيسية
function returnToMainMenu() {
    window.location.href = 'index.html';
}

/*
================================================================================
                                🔢 لعبة الأرقام المتقدمة
================================================================================
*/

function loadAdvancedNumbersGame() {
    console.log('🔢 تحميل لعبة الأرقام المتقدمة...');

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="game-header">
            <h2>🔢 الأرقام المتقدمة (11-20)</h2>
            <p>تعلم الأرقام من 11 إلى 20</p>
        </div>
        <div class="numbers-game">
            <div id="number-display" class="number-display"></div>
            <div id="number-options" class="number-options"></div>
            <button onclick="nextGame()" class="next-btn" style="display:none;">التالي</button>
        </div>
    `;

    showAdvancedNumbersGame();
}

function showAdvancedNumbersGame() {
    let currentIndex = 0;

    function displayNumber() {
        if (currentIndex < advancedNumbersData.length) {
            const item = advancedNumbersData[currentIndex];
            document.getElementById('number-display').innerHTML = `
                <div class="number-card">
                    <div class="number-visual">${item.number}</div>
                    <div class="number-word">${item.word}</div>
                    <div class="number-arabic">${item.arabic}</div>
                    <button onclick="speakWord('${item.word}', 'en')" class="speak-btn">🔊</button>
                </div>
            `;

            setTimeout(() => {
                currentIndex++;
                displayNumber();
            }, 3000);
        } else {
            document.querySelector('.next-btn').style.display = 'block';
        }
    }

    displayNumber();
}

/*
================================================================================
                                🎨 لعبة الألوان المتقدمة
================================================================================
*/

function loadAdvancedColorsGame() {
    console.log('🎨 تحميل لعبة الألوان المتقدمة...');

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="game-header">
            <h2>🎨 الألوان المتقدمة</h2>
            <p>تعلم ألوان جديدة ومتقدمة</p>
        </div>
        <div class="colors-game">
            <div id="color-display" class="color-display"></div>
            <button onclick="nextGame()" class="next-btn" style="display:none;">التالي</button>
        </div>
    `;

    showAdvancedColorsGame();
}

function showAdvancedColorsGame() {
    let currentIndex = 0;

    function displayColor() {
        if (currentIndex < advancedColorsData.length) {
            const item = advancedColorsData[currentIndex];
            document.getElementById('color-display').innerHTML = `
                <div class="color-card">
                    <div class="color-circle" style="background-color: ${item.hex}"></div>
                    <div class="color-word">${item.word}</div>
                    <div class="color-arabic">${item.arabic}</div>
                    <div class="color-emoji">${item.emoji}</div>
                    <button onclick="speakWord('${item.word}', 'en')" class="speak-btn">🔊</button>
                </div>
            `;

            setTimeout(() => {
                currentIndex++;
                displayColor();
            }, 3000);
        } else {
            document.querySelector('.next-btn').style.display = 'block';
        }
    }

    displayColor();
}

/*
================================================================================
                                🏠 لعبة مفردات البيت
================================================================================
*/

function loadHouseVocabularyGame() {
    console.log('🏠 تحميل لعبة مفردات البيت...');

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="game-header">
            <h2>🏠 مفردات البيت</h2>
            <p>تعلم أسماء غرف وأجزاء البيت</p>
        </div>
        <div class="house-game">
            <div id="house-display" class="house-display"></div>
            <button onclick="nextGame()" class="next-btn" style="display:none;">التالي</button>
        </div>
    `;

    showHouseVocabularyGame();
}

function showHouseVocabularyGame() {
    let currentIndex = 0;

    function displayRoom() {
        if (currentIndex < houseVocabularyData.length) {
            const item = houseVocabularyData[currentIndex];
            document.getElementById('house-display').innerHTML = `
                <div class="room-card">
                    <div class="room-emoji">${item.emoji}</div>
                    <div class="room-word">${item.word}</div>
                    <div class="room-arabic">${item.arabic}</div>
                    <button onclick="speakWord('${item.word}', 'en')" class="speak-btn">🔊</button>
                </div>
            `;

            setTimeout(() => {
                currentIndex++;
                displayRoom();
            }, 3000);
        } else {
            document.querySelector('.next-btn').style.display = 'block';
        }
    }

    displayRoom();
}

/*
================================================================================
                                🍕 لعبة الطعام المتقدمة
================================================================================
*/

function loadAdvancedFoodGame() {
    console.log('🍕 تحميل لعبة الطعام المتقدمة...');

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="game-header">
            <h2>🍕 الطعام المتقدم</h2>
            <p>تعلم أسماء أطعمة متنوعة</p>
        </div>
        <div class="food-game">
            <div id="food-display" class="food-display"></div>
            <button onclick="nextGame()" class="next-btn" style="display:none;">التالي</button>
        </div>
    `;

    showAdvancedFoodGame();
}

function showAdvancedFoodGame() {
    let currentIndex = 0;

    function displayFood() {
        if (currentIndex < advancedFoodData.length) {
            const item = advancedFoodData[currentIndex];
            document.getElementById('food-display').innerHTML = `
                <div class="food-card">
                    <div class="food-emoji">${item.emoji}</div>
                    <div class="food-word">${item.word}</div>
                    <div class="food-arabic">${item.arabic}</div>
                    <div class="food-category">${item.category}</div>
                    <button onclick="speakWord('${item.word}', 'en')" class="speak-btn">🔊</button>
                </div>
            `;

            setTimeout(() => {
                currentIndex++;
                displayFood();
            }, 3000);
        } else {
            document.querySelector('.next-btn').style.display = 'block';
        }
    }

    displayFood();
}

/*
================================================================================
                                🎭 لعبة الأنشطة والهوايات
================================================================================
*/

function loadActivitiesGame() {
    console.log('🎭 تحميل لعبة الأنشطة والهوايات...');

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="game-header">
            <h2>🎭 الأنشطة والهوايات</h2>
            <p>تعلم أسماء الأنشطة المختلفة</p>
        </div>
        <div class="activities-game">
            <div id="activity-display" class="activity-display"></div>
            <button onclick="nextGame()" class="next-btn" style="display:none;">التالي</button>
        </div>
    `;

    showActivitiesGame();
}

function showActivitiesGame() {
    let currentIndex = 0;

    function displayActivity() {
        if (currentIndex < activitiesData.length) {
            const item = activitiesData[currentIndex];
            document.getElementById('activity-display').innerHTML = `
                <div class="activity-card">
                    <div class="activity-emoji">${item.emoji}</div>
                    <div class="activity-word">${item.word}</div>
                    <div class="activity-arabic">${item.arabic}</div>
                    <div class="activity-sentence">${item.sentence}</div>
                    <button onclick="speakWord('${item.word}', 'en')" class="speak-btn">🔊</button>
                </div>
            `;

            setTimeout(() => {
                currentIndex++;
                displayActivity();
            }, 3000);
        } else {
            document.querySelector('.next-btn').style.display = 'block';
        }
    }

    displayActivity();
}

/*
================================================================================
                                🦁 لعبة الحيوانات المتقدمة
================================================================================
*/

function loadAdvancedAnimalsGame() {
    console.log('🦁 تحميل لعبة الحيوانات المتقدمة...');

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="game-header">
            <h2>🦁 الحيوانات المتقدمة</h2>
            <p>تعلم أسماء حيوانات جديدة</p>
        </div>
        <div class="animals-game">
            <div id="animal-display" class="animal-display"></div>
            <button onclick="nextGame()" class="next-btn" style="display:none;">التالي</button>
        </div>
    `;

    showAdvancedAnimalsGame();
}

function showAdvancedAnimalsGame() {
    let currentIndex = 0;

    function displayAnimal() {
        if (currentIndex < advancedAnimalsData.length) {
            const item = advancedAnimalsData[currentIndex];
            document.getElementById('animal-display').innerHTML = `
                <div class="animal-card">
                    <div class="animal-emoji">${item.emoji}</div>
                    <div class="animal-word">${item.word}</div>
                    <div class="animal-arabic">${item.arabic}</div>
                    <div class="animal-habitat">Lives in: ${item.habitat}</div>
                    <button onclick="speakWord('${item.word}', 'en')" class="speak-btn">🔊</button>
                </div>
            `;

            setTimeout(() => {
                currentIndex++;
                displayAnimal();
            }, 3000);
        } else {
            document.querySelector('.next-btn').style.display = 'block';
        }
    }

    displayAnimal();
}

/*
================================================================================
                                📚 لعبة القواعد والجمل
================================================================================
*/

function loadGrammarGame() {
    console.log('📚 تحميل لعبة القواعد والجمل...');

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="game-header">
            <h2>📚 القواعد والجمل</h2>
            <p>تعلم قواعد اللغة الإنجليزية البسيطة</p>
        </div>
        <div class="grammar-game">
            <div id="grammar-display" class="grammar-display"></div>
            <button onclick="nextGame()" class="next-btn" style="display:none;">التالي</button>
        </div>
    `;

    showGrammarGame();
}

function showGrammarGame() {
    let currentIndex = 0;

    function displayGrammar() {
        if (currentIndex < grammarData.length) {
            const item = grammarData[currentIndex];
            document.getElementById('grammar-display').innerHTML = `
                <div class="grammar-card">
                    <div class="grammar-question">${item.question}</div>
                    <div class="grammar-options">
                        ${item.options.map(option =>
                            `<button onclick="checkGrammarAnswer('${option}', '${item.correct}')"
                             class="grammar-option">${option}</button>`
                        ).join('')}
                    </div>
                    <div class="grammar-arabic">${item.arabic}</div>
                </div>
            `;
        } else {
            document.querySelector('.next-btn').style.display = 'block';
        }
    }

    function checkGrammarAnswer(selected, correct) {
        if (selected === correct) {
            playSound('correct.mp3');
            setTimeout(() => {
                currentIndex++;
                displayGrammar();
            }, 1500);
        } else {
            playSound('wrong.mp3');
        }
    }

    // جعل الدالة متاحة عالمياً
    window.checkGrammarAnswer = checkGrammarAnswer;

    displayGrammar();
}

/*
================================================================================
                                🔧 دوال مساعدة إضافية
================================================================================
*/

// دالة تشغيل الصوت
function speakWord(word, language) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(word);
        utterance.lang = language === 'ar' ? 'ar-SA' : 'en-US';
        utterance.rate = 0.8;
        speechSynthesis.speak(utterance);
    }
}

// دالة تشغيل الصوت المحسنة للمرحلة الثانية
function enhancedSpeakLevel2(text, language, fallbackSound) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = language === 'ar' ? 'ar-SA' : 'en-US';
        utterance.rate = 0.8;
        speechSynthesis.speak(utterance);
    } else if (fallbackSound) {
        playSound(fallbackSound);
    }
}

// دالة تشغيل ملفات الصوت
function playSound(soundFile) {
    try {
        const audio = new Audio(`sounds/${soundFile}`);
        audio.play().catch(e => console.log('تعذر تشغيل الصوت:', e));
    } catch (error) {
        console.log('خطأ في تشغيل الصوت:', error);
    }
}

// دالة الاحتفال بإكمال اللعبة
function celebrateGameCompletion() {
    // إضافة تأثيرات بصرية للاحتفال
    const celebration = document.createElement('div');
    celebration.className = 'celebration-animation';
    celebration.innerHTML = '🎉🎊✨🌟⭐';
    document.body.appendChild(celebration);

    setTimeout(() => {
        document.body.removeChild(celebration);
    }, 3000);

    playSound('applause.mp3');
}

// دالة تهيئة المرحلة الثانية
function initializeLevel2() {
    console.log('🚀 تهيئة المرحلة الثانية...');
    checkLevel2Resources();
    currentLevel2Game = 0;

    // بدء أول لعبة
    if (level2Games.length > 0) {
        const firstGame = level2Games[0];
        switch(firstGame) {
            case 'advancedNumbers': loadAdvancedNumbersGame(); break;
            case 'advancedColors': loadAdvancedColorsGame(); break;
            case 'houseVocabulary': loadHouseVocabularyGame(); break;
            case 'advancedFood': loadAdvancedFoodGame(); break;
            case 'activities': loadActivitiesGame(); break;
            case 'advancedAnimals': loadAdvancedAnimalsGame(); break;
            case 'grammar': loadGrammarGame(); break;
            default: console.error('لعبة غير معروفة:', firstGame);
        }
    }
}

/*
================================================================================
                                📝 ملاحظات للمطور
================================================================================

✅ تم إنجاز:
- حذف لعبة تلبيس الملابس بالكامل
- استبدالها بلعبة الطعام المتقدمة
- تنظيم الكود مع علامات استرشادية واضحة
- اختصار البيانات والدوال
- إضافة فهرس للألعاب في أعلى الملف

🔧 للتعديل المستقبلي:
1. لحذف لعبة: احذف قسم البيانات + قسم الدالة + أزلها من مصفوفة level2Games
2. لإضافة لعبة: أضف قسم البيانات + قسم الدالة + أضفها لمصفوفة level2Games
3. لتغيير الترتيب: غير ترتيب العناصر في مصفوفة level2Games (السطر 100)

📋 الألعاب الحالية:
1. 🔢 الأرقام المتقدمة (11-20)
2. 🎨 الألوان المتقدمة
3. 🏠 مفردات البيت
4. 🍕 الطعام المتقدمة (بدلاً من الملابس)
5. 🎭 الأنشطة والهوايات
6. 🦁 الحيوانات المتقدمة
7. 📚 القواعد والجمل

================================================================================
*/
