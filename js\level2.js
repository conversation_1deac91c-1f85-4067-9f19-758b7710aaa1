// 🍍 جزيرة الأناناس - المرحلة الثانية (الصف الثاني الابتدائي)

// بيانات الأرقام المتقدمة (11-20)
const advancedNumbersData = [
    { number: 11, word: 'Eleven', arabic: 'أحد عشر', emoji: '🔢', items: 11 },
    { number: 12, word: 'Twelve', arabic: 'اثنا عشر', emoji: '🔢', items: 12 },
    { number: 13, word: 'Thirteen', arabic: 'ثلاثة عشر', emoji: '🔢', items: 13 },
    { number: 14, word: 'Fourteen', arabic: 'أربعة عشر', emoji: '🔢', items: 14 },
    { number: 15, word: 'Fifteen', arabic: 'خمسة عشر', emoji: '🔢', items: 15 },
    { number: 16, word: 'Sixteen', arabic: 'ستة عشر', emoji: '🔢', items: 16 },
    { number: 17, word: 'Seventeen', arabic: 'سبعة عشر', emoji: '🔢', items: 17 },
    { number: 18, word: 'Eighteen', arabic: 'ثمانية عشر', emoji: '🔢', items: 18 },
    { number: 19, word: 'Nineteen', arabic: 'تسعة عشر', emoji: '🔢', items: 19 },
    { number: 20, word: 'Twenty', arabic: 'عشرون', emoji: '🔢', items: 20 }
];

// بيانات الألوان المتقدمة
const advancedColorsData = [
    { color: 'Pink', arabic: 'وردي', hex: '#FFC0CB', items: ['Flower', 'Dress', 'Candy'], emoji: '🌸' },
    { color: 'Brown', arabic: 'بني', hex: '#A52A2A', items: ['Tree', 'Bear', 'Chocolate'], emoji: '🤎' },
    { color: 'Gray', arabic: 'رمادي', hex: '#808080', items: ['Cloud', 'Mouse', 'Stone'], emoji: '🐭' },
    { color: 'Light Blue', arabic: 'أزرق فاتح', hex: '#ADD8E6', items: ['Sky', 'Ice', 'Baby clothes'], emoji: '💙' },
    { color: 'Dark Green', arabic: 'أخضر داكن', hex: '#006400', items: ['Forest', 'Leaf', 'Grass'], emoji: '🌲' }
];

// بيانات البيت والمفردات اليومية
const houseVocabularyData = [
    { word: 'Kitchen', arabic: 'مطبخ', image: 'images/level2/kitchen.png', emoji: '🍳', description: 'Where we cook food' },
    { word: 'Bedroom', arabic: 'غرفة نوم', image: 'images/level2/bedroom.png', emoji: '🛏️', description: 'Where we sleep' },
    { word: 'Living Room', arabic: 'غرفة معيشة', image: 'images/level2/living_room.png', emoji: '🛋️', description: 'Where we sit and watch TV' },
    { word: 'Door', arabic: 'باب', image: 'images/level2/door.png', emoji: '🚪', description: 'We open and close it' },
    { word: 'Window', arabic: 'نافذة', image: 'images/level2/window.png', emoji: '🪟', description: 'We look outside through it' },
    { word: 'Table', arabic: 'طاولة', image: 'images/level2/table.png', emoji: '🪑', description: 'We eat on it' }
];

// بيانات الملابس
const clothesData = [
    { word: 'Shirt', arabic: 'قميص', image: 'images/level2/shirt.png', emoji: '👔', type: 'top', color: 'blue' },
    { word: 'Dress', arabic: 'فستان', image: 'images/level2/dress.png', emoji: '👗', type: 'full', color: 'pink' },
    { word: 'Shoes', arabic: 'حذاء', image: 'images/level2/shoes.png', emoji: '👟', type: 'feet', color: 'black' },
    { word: 'Hat', arabic: 'قبعة', image: 'images/level2/hat.png', emoji: '🎩', type: 'head', color: 'red' },
    { word: 'Pants', arabic: 'بنطلون', image: 'images/level2/pants.png', emoji: '👖', type: 'bottom', color: 'blue' },
    { word: 'Socks', arabic: 'جوارب', image: 'images/level2/socks.png', emoji: '🧦', type: 'feet', color: 'white' }
];

// بيانات الطعام المتقدم
const advancedFoodData = [
    { word: 'Orange', arabic: 'برتقالة', image: 'images/level2/orange.png', emoji: '🍊', category: 'fruits', taste: 'sweet' },
    { word: 'Rice', arabic: 'أرز', image: 'images/level2/rice.png', emoji: '🍚', category: 'grains', taste: 'neutral' },
    { word: 'Egg', arabic: 'بيضة', image: 'images/level2/egg.png', emoji: '🥚', category: 'protein', taste: 'neutral' },
    { word: 'Cheese', arabic: 'جبن', image: 'images/level2/cheese.png', emoji: '🧀', category: 'dairy', taste: 'salty' },
    { word: 'Bread', arabic: 'خبز', image: 'images/level2/bread.png', emoji: '🍞', category: 'grains', taste: 'neutral' },
    { word: 'Soup', arabic: 'شوربة', image: 'images/level2/soup.png', emoji: '🍲', category: 'liquid', taste: 'warm' }
];

// بيانات الهوايات والأنشطة
const activitiesData = [
    { word: 'Play', arabic: 'يلعب', image: 'images/level2/play.png', emoji: '🎮', action: 'playing', sentence: 'I like to play' },
    { word: 'Read', arabic: 'يقرأ', image: 'images/level2/read.png', emoji: '📚', action: 'reading', sentence: 'I like to read' },
    { word: 'Draw', arabic: 'يرسم', image: 'images/level2/draw.png', emoji: '🎨', action: 'drawing', sentence: 'I like to draw' },
    { word: 'Sing', arabic: 'يغني', image: 'images/level2/sing.png', emoji: '🎵', action: 'singing', sentence: 'I like to sing' },
    { word: 'Dance', arabic: 'يرقص', image: 'images/level2/dance.png', emoji: '💃', action: 'dancing', sentence: 'I like to dance' },
    { word: 'Swim', arabic: 'يسبح', image: 'images/level2/swim.png', emoji: '🏊', action: 'swimming', sentence: 'I like to swim' }
];

// بيانات الحيوانات المتقدمة
const advancedAnimalsData = [
    { word: 'Cow', arabic: 'بقرة', image: 'images/level2/cow.png', emoji: '🐄', sound: 'Moo', habitat: 'farm' },
    { word: 'Horse', arabic: 'حصان', image: 'images/level2/horse.png', emoji: '🐴', sound: 'Neigh', habitat: 'farm' },
    { word: 'Rabbit', arabic: 'أرنب', image: 'images/level2/rabbit.png', emoji: '🐰', sound: 'Hop', habitat: 'garden' },
    { word: 'Sheep', arabic: 'خروف', image: 'images/level2/sheep.png', emoji: '🐑', sound: 'Baa', habitat: 'farm' },
    { word: 'Pig', arabic: 'خنزير', image: 'images/level2/pig.png', emoji: '🐷', sound: 'Oink', habitat: 'farm' },
    { word: 'Duck', arabic: 'بطة', image: 'images/level2/duck.png', emoji: '🦆', sound: 'Quack', habitat: 'pond' }
];

// بيانات القواعد والجمل
const grammarData = {
    pronouns: [
        { word: 'I', arabic: 'أنا', example: 'I like apples', emoji: '👤' },
        { word: 'You', arabic: 'أنت', example: 'You are nice', emoji: '👥' },
        { word: 'He', arabic: 'هو', example: 'He plays football', emoji: '👨' },
        { word: 'She', arabic: 'هي', example: 'She reads books', emoji: '👩' },
        { word: 'It', arabic: 'هو/هي (غير عاقل)', example: 'It is red', emoji: '📦' },
        { word: 'We', arabic: 'نحن', example: 'We are friends', emoji: '👫' },
        { word: 'They', arabic: 'هم', example: 'They are happy', emoji: '👥' }
    ],
    verbs: [
        { word: 'like', arabic: 'يحب', forms: { I: 'like', He: 'likes', She: 'likes' } },
        { word: 'play', arabic: 'يلعب', forms: { I: 'play', He: 'plays', She: 'plays' } },
        { word: 'eat', arabic: 'يأكل', forms: { I: 'eat', He: 'eats', She: 'eats' } },
        { word: 'go', arabic: 'يذهب', forms: { I: 'go', He: 'goes', She: 'goes' } },
        { word: 'read', arabic: 'يقرأ', forms: { I: 'read', He: 'reads', She: 'reads' } }
    ],
    questions: [
        { word: 'What', arabic: 'ماذا', example: 'What is this?', emoji: '❓' },
        { word: 'Where', arabic: 'أين', example: 'Where is the cat?', emoji: '📍' },
        { word: 'Who', arabic: 'من', example: 'Who is that?', emoji: '👤' },
        { word: 'How', arabic: 'كيف', example: 'How are you?', emoji: '🤔' }
    ],
    canStatements: [
        { subject: 'I', verb: 'swim', sentence: 'I can swim', negative: "I can't swim" },
        { subject: 'You', verb: 'read', sentence: 'You can read', negative: "You can't read" },
        { subject: 'He', verb: 'play', sentence: 'He can play', negative: "He can't play" },
        { subject: 'She', verb: 'sing', sentence: 'She can sing', negative: "She can't sing" }
    ]
};

// وظائف ألعاب المرحلة الثانية
function loadLevel2Game(gameIndex) {
    switch(gameIndex) {
        case 0:
            loadAdvancedNumbersGame();
            break;
        case 1:
            loadAdvancedColorsGame();
            break;
        case 2:
            loadHouseVocabularyGame();
            break;
        case 3:
            loadClothesGame();
            break;
        case 4:
            loadAdvancedFoodGame();
            break;
        case 5:
            loadActivitiesGame();
            break;
        case 6:
            loadAdvancedAnimalsGame();
            break;
        case 7:
            loadGrammarGame();
            break;
        default:
            console.log('لعبة غير موجودة في المرحلة الثانية');
    }
}

// وظائف مساعدة للمرحلة الثانية
function enhancedSpeakLevel2(text, language = 'en', audioFile = null) {
    // محاولة تشغيل الملف الصوتي أولاً
    if (audioFile) {
        const audio = new Audio(`sounds/level2/${audioFile}`);
        audio.play().catch(() => {
            // في حالة فشل تشغيل الملف، استخدم النطق المدمج
            fallbackToSpeechSynthesis(text, language);
        });
    } else {
        fallbackToSpeechSynthesis(text, language);
    }
}

function fallbackToSpeechSynthesis(text, language) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = language === 'ar' ? 'ar-SA' : 'en-US';
        utterance.rate = 0.8;
        utterance.pitch = 1.1;
        speechSynthesis.speak(utterance);
    }
}

// وظائف التحقق من الموارد
function checkLevel2Resources() {
    const requiredImages = [
        'kitchen.png', 'bedroom.png', 'living_room.png',
        'door.png', 'window.png', 'table.png',
        'shirt.png', 'dress.png', 'shoes.png', 'hat.png',
        'orange.png', 'rice.png', 'egg.png', 'cheese.png',
        'play.png', 'read.png', 'draw.png', 'sing.png',
        'cow.png', 'horse.png', 'rabbit.png'
    ];
    
    console.log('🔍 فحص موارد المرحلة الثانية...');
    // يمكن إضافة منطق فحص الموارد هنا
}

// لعبة الأرقام المتقدمة (11-20)
function loadAdvancedNumbersGame() {
    let selectedNumber = null;
    let placedNumbers = 0;
    const totalNumbers = 10; // من 11 إلى 20
    const numbers = advancedNumbersData.slice(0, totalNumbers);
    const shuffledNumbers = shuffleArray([...numbers]);

    function showAdvancedNumbersGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-numbers-game">
                <h2>🔢 لعبة الأرقام المتقدمة (11-20)</h2>
                <p class="game-instruction">انقر على الرقم ثم انقر على مكانه الصحيح</p>
                <p class="game-instruction-en">Click on a number then click on its correct position</p>

                <div class="advanced-numbers-container">
                    <div class="numbers-pool">
                        <h3>الأرقام</h3>
                        <div class="clickable-advanced-numbers">
                            ${shuffledNumbers.map((num, index) => `
                                <div class="clickable-advanced-number"
                                     data-number="${num.number}"
                                     id="advanced-number-${num.number}">
                                    <span class="number-word">${num.word}</span>
                                    <span class="number-value">${num.number}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="sequence-line">
                        <h3>رتب الأرقام هنا (11-20)</h3>
                        <div class="advanced-sequence-slots">
                            ${numbers.map(num => `
                                <div class="advanced-sequence-slot"
                                     data-position="${num.number}"
                                     id="advanced-slot-${num.number}">
                                    <div class="slot-number">${num.number}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="counting-section">
                    <h3>تعلم العد</h3>
                    <div class="counting-display" id="counting-display">
                        <div class="counting-items"></div>
                        <div class="counting-text">انقر على رقم لرؤية العد</div>
                    </div>
                </div>

                <div class="sequence-progress">
                    <div class="progress-text">الأرقام المرتبة: <span id="advanced-placed-count">0</span> من ${totalNumbers}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="advanced-sequence-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedNumbersClickEvents();
    }

    function setupAdvancedNumbersClickEvents() {
        const clickableNumbers = document.querySelectorAll('.clickable-advanced-number');
        const sequenceSlots = document.querySelectorAll('.advanced-sequence-slot');

        // إعداد الأرقام القابلة للنقر
        clickableNumbers.forEach(number => {
            number.addEventListener('click', handleAdvancedNumberClick);
            number.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleAdvancedNumberClick.call(this, e);
            });
        });

        // إعداد مواضع الترتيب
        sequenceSlots.forEach(slot => {
            slot.addEventListener('click', handleAdvancedSlotClick);
            slot.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleAdvancedSlotClick.call(this, e);
            });
        });
    }

    function handleAdvancedNumberClick(e) {
        e.preventDefault();

        // إلغاء تحديد الرقم السابق
        if (selectedNumber) {
            selectedNumber.classList.remove('selected');
        }

        // تحديد الرقم الجديد
        selectedNumber = this;
        selectedNumber.classList.add('selected');

        // نطق اسم الرقم بالإنجليزية
        const numberValue = parseInt(selectedNumber.dataset.number);
        const numberData = advancedNumbersData.find(n => n.number === numberValue);
        enhancedSpeakLevel2(numberData.word, 'en', `${numberData.word.toLowerCase()}_en.mp3`);

        // عرض العد التفاعلي
        showCountingDisplay(numberValue);

        // تأثير بصري
        selectedNumber.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedNumber) {
                selectedNumber.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handleAdvancedSlotClick(e) {
        e.preventDefault();

        if (!selectedNumber) {
            enhancedSpeakLevel2('اختر رقماً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للأرقام
            const numbersPool = document.querySelector('.clickable-advanced-numbers');
            if (numbersPool) {
                numbersPool.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    numbersPool.style.animation = '';
                }, 600);
            }
            return;
        }

        const slot = this;
        placeAdvancedNumberInSlot(selectedNumber, slot);
    }

    function placeAdvancedNumberInSlot(numberElement, slot) {
        const numberValue = parseInt(numberElement.dataset.number);
        const slotPosition = parseInt(slot.dataset.position);

        // التحقق من وجود رقم في الموضع
        const existingNumber = slot.querySelector('.placed-advanced-number');
        if (existingNumber) {
            // إرجاع الرقم الموجود إلى المجموعة
            returnAdvancedNumberToPool(existingNumber);
        }

        // وضع الرقم الجديد
        const placedNumber = numberElement.cloneNode(true);
        placedNumber.classList.add('placed-advanced-number');
        placedNumber.classList.remove('selected');

        // إزالة أحداث النقر من الرقم المنسوخ
        placedNumber.removeEventListener('click', handleAdvancedNumberClick);

        slot.appendChild(placedNumber);

        // إخفاء الرقم الأصلي
        numberElement.style.display = 'none';

        // إلغاء التحديد
        selectedNumber = null;

        // تحديث التقدم
        updateAdvancedSequenceProgress();

        // تأثيرات بصرية
        if (numberValue === slotPosition) {
            slot.classList.add('correct-position');
            enhancedSpeakLevel2('موضع صحيح!', 'ar', 'correct.mp3');
        } else {
            slot.classList.add('incorrect-position');
        }

        // تأثير بصري للوضع
        slot.style.transform = 'scale(1.1)';
        setTimeout(() => {
            slot.style.transform = 'scale(1)';
        }, 300);
    }

    function returnAdvancedNumberToPool(placedNumber) {
        const numberValue = placedNumber.dataset.number;
        const originalNumber = document.getElementById(`advanced-number-${numberValue}`);
        if (originalNumber) {
            originalNumber.style.display = 'block';
            originalNumber.classList.remove('selected');
        }

        // إزالة الرقم من الموضع
        const slot = placedNumber.closest('.advanced-sequence-slot');
        slot.classList.remove('correct-position', 'incorrect-position');
        placedNumber.remove();

        updateAdvancedSequenceProgress();
    }

    function updateAdvancedSequenceProgress() {
        const placedNumbers = document.querySelectorAll('.placed-advanced-number').length;
        const placedCount = document.getElementById('advanced-placed-count');
        const progressFill = document.getElementById('advanced-sequence-progress-fill');

        if (placedCount) {
            placedCount.textContent = placedNumbers;
        }

        if (progressFill) {
            const percentage = (placedNumbers / totalNumbers) * 100;
            progressFill.style.width = percentage + '%';
        }

        // فحص تلقائي عند اكتمال جميع الأرقام
        if (placedNumbers === totalNumbers) {
            setTimeout(() => {
                checkAdvancedSequenceAutomatically();
            }, 500);
        }
    }

    function checkAdvancedSequenceAutomatically() {
        let correctCount = 0;
        const slots = document.querySelectorAll('.advanced-sequence-slot');

        slots.forEach(slot => {
            const placedNumber = slot.querySelector('.placed-advanced-number');
            if (placedNumber) {
                const numberValue = parseInt(placedNumber.dataset.number);
                const slotPosition = parseInt(slot.dataset.position);

                if (numberValue === slotPosition) {
                    correctCount++;
                    slot.classList.add('final-correct');
                    recordAnswer(true);
                } else {
                    slot.classList.add('final-incorrect');
                    recordAnswer(false);
                }
            }
        });

        if (correctCount === totalNumbers) {
            // ترتيب صحيح كامل
            enhancedSpeakLevel2('ممتاز! رتبت جميع الأرقام بشكل صحيح!', 'ar', 'excellent.mp3');
            celebrateGameCompletion();

            setTimeout(() => {
                nextGame();
            }, 3000);
        } else {
            // ترتيب غير مكتمل
            enhancedSpeakLevel2(`أحسنت! ${correctCount} أرقام في المكان الصحيح من ${totalNumbers}`, 'ar', 'good.mp3');

            setTimeout(() => {
                // إعادة تعيين الأرقام الخاطئة
                resetIncorrectAdvancedNumbers();
            }, 2000);
        }
    }

    function resetIncorrectAdvancedNumbers() {
        const incorrectSlots = document.querySelectorAll('.advanced-sequence-slot.final-incorrect');
        incorrectSlots.forEach(slot => {
            const placedNumber = slot.querySelector('.placed-advanced-number');
            if (placedNumber) {
                returnAdvancedNumberToPool(placedNumber);
            }
        });
    }

    function showCountingDisplay(number) {
        const countingDisplay = document.getElementById('counting-display');
        const countingItems = countingDisplay.querySelector('.counting-items');
        const countingText = countingDisplay.querySelector('.counting-text');

        // إنشاء عناصر العد
        countingItems.innerHTML = '';
        for (let i = 1; i <= number; i++) {
            const item = document.createElement('div');
            item.className = 'counting-item';
            item.textContent = '⭐';
            item.style.animationDelay = `${i * 0.1}s`;
            countingItems.appendChild(item);
        }

        countingText.textContent = `${number} نجوم`;

        // تأثير العد
        setTimeout(() => {
            enhancedSpeakLevel2(`Count: ${number}`, 'en', `count_${number}_en.mp3`);
        }, 500);
    }

    showAdvancedNumbersGame();
}

// وظائف مساعدة إضافية
function celebrateGameCompletion() {
    // تأثير الاحتفال
    const gameContent = document.getElementById('game-content');
    if (gameContent) {
        gameContent.style.animation = 'celebrate 1s ease-in-out';
        setTimeout(() => {
            gameContent.style.animation = '';
        }, 1000);
    }
}

// إضافة تأثير الاحتفال في CSS
const celebrateStyle = document.createElement('style');
celebrateStyle.textContent = `
    @keyframes celebrate {
        0%, 100% { transform: scale(1); }
        25% { transform: scale(1.02); }
        50% { transform: scale(1.05); }
        75% { transform: scale(1.02); }
    }
`;
document.head.appendChild(celebrateStyle);

// لعبة الألوان المتقدمة
function loadAdvancedColorsGame() {
    let selectedColor = null;
    let matchedPairs = 0;
    const totalPairs = advancedColorsData.length;
    const shuffledColors = shuffleArray([...advancedColorsData]);

    function showAdvancedColorsGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-colors-game">
                <h2>🌈 لعبة الألوان المتقدمة</h2>
                <p class="game-instruction">انقر على اللون ثم انقر على الشيء الذي يحمل نفس اللون</p>
                <p class="game-instruction-en">Click on a color then click on an item with the same color</p>

                <div class="advanced-colors-container">
                    <div class="colors-palette">
                        <h3>الألوان</h3>
                        <div class="clickable-colors">
                            ${shuffledColors.map(color => `
                                <div class="clickable-color"
                                     data-color="${color.color}"
                                     style="background-color: ${color.hex}">
                                    <span class="color-name">${color.color}</span>
                                    <span class="color-emoji">${color.emoji}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="color-items">
                        <h3>الأشياء</h3>
                        <div class="clickable-items">
                            ${shuffledColors.map(color =>
                                color.items.map(item => `
                                    <div class="clickable-item"
                                         data-color="${color.color}"
                                         data-item="${item}">
                                        <div class="item-icon" style="background-color: ${color.hex}">
                                            ${color.emoji}
                                        </div>
                                        <span class="item-name">${item}</span>
                                    </div>
                                `).join('')
                            ).join('')}
                        </div>
                    </div>
                </div>

                <div class="color-matching-progress">
                    <div class="progress-text">الألوان المطابقة: <span id="matched-colors-count">0</span> من ${totalPairs}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="color-matching-progress-fill" style="width: 0%"></div>
                    </div>
                </div>

                <div class="color-learning-section">
                    <h3>تعلم الألوان</h3>
                    <div class="color-display" id="color-display">
                        <div class="color-preview"></div>
                        <div class="color-info">انقر على لون لتعلم المزيد عنه</div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedColorsClickEvents();
    }

    function setupAdvancedColorsClickEvents() {
        const clickableColors = document.querySelectorAll('.clickable-color');
        const clickableItems = document.querySelectorAll('.clickable-item');

        // إعداد الألوان القابلة للنقر
        clickableColors.forEach(color => {
            color.addEventListener('click', handleColorClick);
            color.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleColorClick.call(this, e);
            });
        });

        // إعداد الأشياء القابلة للنقر
        clickableItems.forEach(item => {
            item.addEventListener('click', handleItemClick);
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleItemClick.call(this, e);
            });
        });
    }

    function handleColorClick(e) {
        e.preventDefault();

        // إلغاء تحديد اللون السابق
        if (selectedColor) {
            selectedColor.classList.remove('selected');
        }

        // تحديد اللون الجديد
        selectedColor = this;
        selectedColor.classList.add('selected');

        // نطق اسم اللون بالإنجليزية
        const colorName = selectedColor.dataset.color;
        enhancedSpeakLevel2(colorName, 'en', `${colorName.toLowerCase().replace(' ', '_')}_en.mp3`);

        // عرض معلومات اللون
        showColorInfo(colorName);

        // تأثير بصري
        selectedColor.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedColor) {
                selectedColor.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handleItemClick(e) {
        e.preventDefault();

        if (!selectedColor) {
            enhancedSpeakLevel2('اختر لوناً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للألوان
            const colorsArea = document.querySelector('.colors-palette');
            if (colorsArea) {
                colorsArea.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    colorsArea.style.animation = '';
                }, 600);
            }
            return;
        }

        const item = this;
        checkColorMatch(selectedColor, item);
    }

    function checkColorMatch(colorElement, itemElement) {
        const selectedColorName = colorElement.dataset.color;
        const itemColorName = itemElement.dataset.color;
        const itemName = itemElement.dataset.item;

        if (selectedColorName === itemColorName) {
            // مطابقة صحيحة
            handleCorrectColorMatch(colorElement, itemElement);
        } else {
            // مطابقة خاطئة
            handleIncorrectColorMatch(itemElement);
        }
    }

    function handleCorrectColorMatch(colorElement, itemElement) {
        // تأثيرات بصرية للنجاح
        colorElement.classList.add('matched');
        itemElement.classList.add('matched');

        // إخفاء العناصر المطابقة
        setTimeout(() => {
            colorElement.style.display = 'none';
            itemElement.style.display = 'none';
        }, 1000);

        // تسجيل النقاط والتقدم
        matchedPairs++;
        recordAnswer(true);
        updateColorMatchingProgress();

        // تأثيرات صوتية
        enhancedSpeakLevel2('ممتاز! مطابقة صحيحة!', 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        // إلغاء التحديد
        selectedColor = null;

        // التحقق من اكتمال اللعبة
        if (matchedPairs >= totalPairs) {
            setTimeout(() => {
                completeAdvancedColorsGame();
            }, 1500);
        }
    }

    function handleIncorrectColorMatch(itemElement) {
        // تأثير اهتزاز للعنصر
        itemElement.classList.add('shake-animation');
        enhancedSpeakLevel2('حاول مرة أخرى', 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            itemElement.classList.remove('shake-animation');
            // إلغاء التحديد بعد المحاولة الخاطئة
            if (selectedColor) {
                selectedColor.classList.remove('selected');
                selectedColor = null;
            }
        }, 600);
    }

    function showColorInfo(colorName) {
        const colorDisplay = document.getElementById('color-display');
        const colorPreview = colorDisplay.querySelector('.color-preview');
        const colorInfo = colorDisplay.querySelector('.color-info');

        const colorData = advancedColorsData.find(c => c.color === colorName);
        if (colorData) {
            colorPreview.style.backgroundColor = colorData.hex;
            colorPreview.textContent = colorData.emoji;
            colorInfo.innerHTML = `
                <strong>${colorData.color}</strong><br>
                <span style="color: #6c757d;">${colorData.arabic}</span><br>
                <small>أشياء بهذا اللون: ${colorData.items.join(', ')}</small>
            `;
        }
    }

    function updateColorMatchingProgress() {
        const matchedCount = document.getElementById('matched-colors-count');
        const progressFill = document.getElementById('color-matching-progress-fill');

        if (matchedCount) {
            matchedCount.textContent = matchedPairs;
        }

        if (progressFill) {
            const percentage = (matchedPairs / totalPairs) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeAdvancedColorsGame() {
        enhancedSpeakLevel2('رائع! أكملت لعبة الألوان المتقدمة!', 'ar', 'level_complete.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showAdvancedColorsGame();
}

// تهيئة المرحلة الثانية
function initializeLevel2() {
    console.log('🍍 تهيئة المرحلة الثانية - الصف الثاني الابتدائي');
    checkLevel2Resources();
}
