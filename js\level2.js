// 🍍 جزيرة الأناناس - المرحلة الثانية (الصف الثاني الابتدائي)

// بيانات الأرقام المتقدمة (11-20)
const advancedNumbersData = [
    { number: 11, word: 'Eleven', arabic: 'أحد عشر', emoji: '🔢', items: 11 },
    { number: 12, word: 'Twelve', arabic: 'اثنا عشر', emoji: '🔢', items: 12 },
    { number: 13, word: 'Thirteen', arabic: 'ثلاثة عشر', emoji: '🔢', items: 13 },
    { number: 14, word: 'Fourteen', arabic: 'أربعة عشر', emoji: '🔢', items: 14 },
    { number: 15, word: 'Fifteen', arabic: 'خمسة عشر', emoji: '🔢', items: 15 },
    { number: 16, word: 'Sixteen', arabic: 'ستة عشر', emoji: '🔢', items: 16 },
    { number: 17, word: 'Seventeen', arabic: 'سبعة عشر', emoji: '🔢', items: 17 },
    { number: 18, word: 'Eighteen', arabic: 'ثمانية عشر', emoji: '🔢', items: 18 },
    { number: 19, word: 'Nineteen', arabic: 'تسعة عشر', emoji: '🔢', items: 19 },
    { number: 20, word: 'Twenty', arabic: 'عشرون', emoji: '🔢', items: 20 }
];

// بيانات الألوان المتقدمة
const advancedColorsData = [
    { color: 'Pink', arabic: 'وردي', hex: '#FFC0CB', items: ['Flower', 'Dress', 'Candy'], emoji: '🌸' },
    { color: 'Brown', arabic: 'بني', hex: '#A52A2A', items: ['Tree', 'Bear', 'Chocolate'], emoji: '🤎' },
    { color: 'Gray', arabic: 'رمادي', hex: '#808080', items: ['Cloud', 'Mouse', 'Stone'], emoji: '🐭' },
    { color: 'Light Blue', arabic: 'أزرق فاتح', hex: '#ADD8E6', items: ['Sky', 'Ice', 'Baby clothes'], emoji: '💙' },
    { color: 'Dark Green', arabic: 'أخضر داكن', hex: '#006400', items: ['Forest', 'Leaf', 'Grass'], emoji: '🌲' }
];

// بيانات البيت والمفردات اليومية
const houseVocabularyData = [
    { word: 'Kitchen', arabic: 'مطبخ', image: 'images/level2/kitchen.png', emoji: '🍳', description: 'Where we cook food' },
    { word: 'Bedroom', arabic: 'غرفة نوم', image: 'images/level2/bedroom.png', emoji: '🛏️', description: 'Where we sleep' },
    { word: 'Living Room', arabic: 'غرفة معيشة', image: 'images/level2/living_room.png', emoji: '🛋️', description: 'Where we sit and watch TV' },
    { word: 'Door', arabic: 'باب', image: 'images/level2/door.png', emoji: '🚪', description: 'We open and close it' },
    { word: 'Window', arabic: 'نافذة', image: 'images/level2/window.png', emoji: '🪟', description: 'We look outside through it' },
    { word: 'Table', arabic: 'طاولة', image: 'images/level2/table.png', emoji: '🪑', description: 'We eat on it' }
];

// تم حذف بيانات الملابس

// بيانات الطعام المتقدم
const advancedFoodData = [
    { word: 'Orange', arabic: 'برتقالة', image: 'images/level2/orange.png', emoji: '🍊', category: 'fruits', taste: 'sweet' },
    { word: 'Rice', arabic: 'أرز', image: 'images/level2/rice.png', emoji: '🍚', category: 'grains', taste: 'neutral' },
    { word: 'Egg', arabic: 'بيضة', image: 'images/level2/egg.png', emoji: '🥚', category: 'protein', taste: 'neutral' },
    { word: 'Cheese', arabic: 'جبن', image: 'images/level2/cheese.png', emoji: '🧀', category: 'dairy', taste: 'salty' },
    { word: 'Bread', arabic: 'خبز', image: 'images/level2/bread.png', emoji: '🍞', category: 'grains', taste: 'neutral' },
    { word: 'Soup', arabic: 'شوربة', image: 'images/level2/soup.png', emoji: '🍲', category: 'liquid', taste: 'warm' }
];

// بيانات الهوايات والأنشطة
const activitiesData = [
    { word: 'Play', arabic: 'يلعب', image: 'images/level2/play.png', emoji: '🎮', action: 'playing', sentence: 'I like to play' },
    { word: 'Read', arabic: 'يقرأ', image: 'images/level2/read.png', emoji: '📚', action: 'reading', sentence: 'I like to read' },
    { word: 'Draw', arabic: 'يرسم', image: 'images/level2/draw.png', emoji: '🎨', action: 'drawing', sentence: 'I like to draw' },
    { word: 'Sing', arabic: 'يغني', image: 'images/level2/sing.png', emoji: '🎵', action: 'singing', sentence: 'I like to sing' },
    { word: 'Dance', arabic: 'يرقص', image: 'images/level2/dance.png', emoji: '💃', action: 'dancing', sentence: 'I like to dance' },
    { word: 'Swim', arabic: 'يسبح', image: 'images/level2/swim.png', emoji: '🏊', action: 'swimming', sentence: 'I like to swim' }
];

// بيانات الحيوانات المتقدمة
const advancedAnimalsData = [
    { word: 'Cow', arabic: 'بقرة', image: 'images/level2/cow.png', emoji: '🐄', sound: 'Moo', habitat: 'farm' },
    { word: 'Horse', arabic: 'حصان', image: 'images/level2/horse.png', emoji: '🐴', sound: 'Neigh', habitat: 'farm' },
    { word: 'Rabbit', arabic: 'أرنب', image: 'images/level2/rabbit.png', emoji: '🐰', sound: 'Hop', habitat: 'garden' },
    { word: 'Sheep', arabic: 'خروف', image: 'images/level2/sheep.png', emoji: '🐑', sound: 'Baa', habitat: 'farm' },
    { word: 'Pig', arabic: 'خنزير', image: 'images/level2/pig.png', emoji: '🐷', sound: 'Oink', habitat: 'farm' },
    { word: 'Duck', arabic: 'بطة', image: 'images/level2/duck.png', emoji: '🦆', sound: 'Quack', habitat: 'pond' }
];

// بيانات القواعد والجمل
const grammarData = {
    pronouns: [
        { word: 'I', arabic: 'أنا', example: 'I like apples', emoji: '👤' },
        { word: 'You', arabic: 'أنت', example: 'You are nice', emoji: '👥' },
        { word: 'He', arabic: 'هو', example: 'He plays football', emoji: '👨' },
        { word: 'She', arabic: 'هي', example: 'She reads books', emoji: '👩' },
        { word: 'It', arabic: 'هو/هي (غير عاقل)', example: 'It is red', emoji: '📦' },
        { word: 'We', arabic: 'نحن', example: 'We are friends', emoji: '👫' },
        { word: 'They', arabic: 'هم', example: 'They are happy', emoji: '👥' }
    ],
    verbs: [
        { word: 'like', arabic: 'يحب', forms: { I: 'like', He: 'likes', She: 'likes' } },
        { word: 'play', arabic: 'يلعب', forms: { I: 'play', He: 'plays', She: 'plays' } },
        { word: 'eat', arabic: 'يأكل', forms: { I: 'eat', He: 'eats', She: 'eats' } },
        { word: 'go', arabic: 'يذهب', forms: { I: 'go', He: 'goes', She: 'goes' } },
        { word: 'read', arabic: 'يقرأ', forms: { I: 'read', He: 'reads', She: 'reads' } }
    ],
    questions: [
        { word: 'What', arabic: 'ماذا', example: 'What is this?', emoji: '❓' },
        { word: 'Where', arabic: 'أين', example: 'Where is the cat?', emoji: '📍' },
        { word: 'Who', arabic: 'من', example: 'Who is that?', emoji: '👤' },
        { word: 'How', arabic: 'كيف', example: 'How are you?', emoji: '🤔' }
    ],
    canStatements: [
        { subject: 'I', verb: 'swim', sentence: 'I can swim', negative: "I can't swim" },
        { subject: 'You', verb: 'read', sentence: 'You can read', negative: "You can't read" },
        { subject: 'He', verb: 'play', sentence: 'He can play', negative: "He can't play" },
        { subject: 'She', verb: 'sing', sentence: 'She can sing', negative: "She can't sing" }
    ]
};

// وظائف ألعاب المرحلة الثانية
function loadLevel2Game(gameIndex) {
    switch(gameIndex) {
        case 0:
            loadAdvancedNumbersGame();
            break;
        case 1:
            loadAdvancedColorsGame();
            break;
        case 2:
            loadHouseVocabularyGame();
            break;
        case 3:
            loadClothesGame(); // الآن تحمل لعبة الطعام المتقدمة
            break;
        case 4:
            loadActivitiesGame();
            break;
        case 5:
            loadAdvancedAnimalsGame();
            break;
        case 6:
            loadGrammarGame();
            break;
        default:
            console.log('لعبة غير موجودة في المرحلة الثانية');
    }
}

// وظائف مساعدة للمرحلة الثانية
function enhancedSpeakLevel2(text, language = 'en', audioFile = null) {
    // محاولة تشغيل الملف الصوتي أولاً
    if (audioFile) {
        const audio = new Audio(`sounds/level2/${audioFile}`);
        audio.play().catch(() => {
            // في حالة فشل تشغيل الملف، استخدم النطق المدمج
            fallbackToSpeechSynthesis(text, language);
        });
    } else {
        fallbackToSpeechSynthesis(text, language);
    }
}

function fallbackToSpeechSynthesis(text, language) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = language === 'ar' ? 'ar-SA' : 'en-US';
        utterance.rate = 0.8;
        utterance.pitch = 1.1;
        speechSynthesis.speak(utterance);
    }
}

// وظائف التحقق من الموارد
function checkLevel2Resources() {
    const requiredImages = [
        'kitchen.png', 'bedroom.png', 'living_room.png',
        'door.png', 'window.png', 'table.png',
        'shirt.png', 'dress.png', 'shoes.png', 'hat.png',
        'orange.png', 'rice.png', 'egg.png', 'cheese.png',
        'play.png', 'read.png', 'draw.png', 'sing.png',
        'cow.png', 'horse.png', 'rabbit.png'
    ];
    
    console.log('🔍 فحص موارد المرحلة الثانية...');
    // يمكن إضافة منطق فحص الموارد هنا
}

// لعبة الأرقام المتقدمة (11-20)
function loadAdvancedNumbersGame() {
    let selectedNumber = null;
    let placedNumbers = 0;
    const totalNumbers = 10; // من 11 إلى 20
    const numbers = advancedNumbersData.slice(0, totalNumbers);
    const shuffledNumbers = shuffleArray([...numbers]);

    function showAdvancedNumbersGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-numbers-game">
                <h2>🔢 لعبة الأرقام المتقدمة (11-20)</h2>
                <p class="game-instruction">انقر على الرقم ثم انقر على مكانه الصحيح</p>
                <p class="game-instruction-en">Click on a number then click on its correct position</p>

                <div class="advanced-numbers-container">
                    <div class="numbers-pool">
                        <h3>الأرقام</h3>
                        <div class="clickable-advanced-numbers">
                            ${shuffledNumbers.map((num, index) => `
                                <div class="clickable-advanced-number"
                                     data-number="${num.number}"
                                     id="advanced-number-${num.number}">
                                    <span class="number-word">${num.word}</span>
                                    <span class="number-value">${num.number}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="sequence-line">
                        <h3>رتب الأرقام هنا (11-20)</h3>
                        <div class="advanced-sequence-slots">
                            ${numbers.map(num => `
                                <div class="advanced-sequence-slot"
                                     data-position="${num.number}"
                                     id="advanced-slot-${num.number}">
                                    <div class="slot-number">${num.number}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="counting-section">
                    <h3>تعلم العد</h3>
                    <div class="counting-display" id="counting-display">
                        <div class="counting-items"></div>
                        <div class="counting-text">انقر على رقم لرؤية العد</div>
                    </div>
                </div>

                <div class="sequence-progress">
                    <div class="progress-text">الأرقام المرتبة: <span id="advanced-placed-count">0</span> من ${totalNumbers}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="advanced-sequence-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedNumbersClickEvents();
    }

    function setupAdvancedNumbersClickEvents() {
        const clickableNumbers = document.querySelectorAll('.clickable-advanced-number');
        const sequenceSlots = document.querySelectorAll('.advanced-sequence-slot');

        // إعداد الأرقام القابلة للنقر
        clickableNumbers.forEach(number => {
            number.addEventListener('click', handleAdvancedNumberClick);
            number.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleAdvancedNumberClick.call(this, e);
            });
        });

        // إعداد مواضع الترتيب
        sequenceSlots.forEach(slot => {
            slot.addEventListener('click', handleAdvancedSlotClick);
            slot.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleAdvancedSlotClick.call(this, e);
            });
        });
    }

    function handleAdvancedNumberClick(e) {
        e.preventDefault();

        // إلغاء تحديد الرقم السابق
        if (selectedNumber) {
            selectedNumber.classList.remove('selected');
        }

        // تحديد الرقم الجديد
        selectedNumber = this;
        selectedNumber.classList.add('selected');

        // نطق اسم الرقم بالإنجليزية
        const numberValue = parseInt(selectedNumber.dataset.number);
        const numberData = advancedNumbersData.find(n => n.number === numberValue);
        enhancedSpeakLevel2(numberData.word, 'en', `${numberData.word.toLowerCase()}_en.mp3`);

        // عرض العد التفاعلي
        showCountingDisplay(numberValue);

        // تأثير بصري
        selectedNumber.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedNumber) {
                selectedNumber.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handleAdvancedSlotClick(e) {
        e.preventDefault();

        if (!selectedNumber) {
            enhancedSpeakLevel2('اختر رقماً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للأرقام
            const numbersPool = document.querySelector('.clickable-advanced-numbers');
            if (numbersPool) {
                numbersPool.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    numbersPool.style.animation = '';
                }, 600);
            }
            return;
        }

        const slot = this;
        placeAdvancedNumberInSlot(selectedNumber, slot);
    }

    function placeAdvancedNumberInSlot(numberElement, slot) {
        const numberValue = parseInt(numberElement.dataset.number);
        const slotPosition = parseInt(slot.dataset.position);

        // التحقق من وجود رقم في الموضع
        const existingNumber = slot.querySelector('.placed-advanced-number');
        if (existingNumber) {
            // إرجاع الرقم الموجود إلى المجموعة
            returnAdvancedNumberToPool(existingNumber);
        }

        // وضع الرقم الجديد
        const placedNumber = numberElement.cloneNode(true);
        placedNumber.classList.add('placed-advanced-number');
        placedNumber.classList.remove('selected');

        // إزالة أحداث النقر من الرقم المنسوخ
        placedNumber.removeEventListener('click', handleAdvancedNumberClick);

        slot.appendChild(placedNumber);

        // إخفاء الرقم الأصلي
        numberElement.style.display = 'none';

        // إلغاء التحديد
        selectedNumber = null;

        // تحديث التقدم
        updateAdvancedSequenceProgress();

        // تأثيرات بصرية
        if (numberValue === slotPosition) {
            slot.classList.add('correct-position');
            enhancedSpeakLevel2('موضع صحيح!', 'ar', 'correct.mp3');
        } else {
            slot.classList.add('incorrect-position');
        }

        // تأثير بصري للوضع
        slot.style.transform = 'scale(1.1)';
        setTimeout(() => {
            slot.style.transform = 'scale(1)';
        }, 300);
    }

    function returnAdvancedNumberToPool(placedNumber) {
        const numberValue = placedNumber.dataset.number;
        const originalNumber = document.getElementById(`advanced-number-${numberValue}`);
        if (originalNumber) {
            originalNumber.style.display = 'block';
            originalNumber.classList.remove('selected');
        }

        // إزالة الرقم من الموضع
        const slot = placedNumber.closest('.advanced-sequence-slot');
        slot.classList.remove('correct-position', 'incorrect-position');
        placedNumber.remove();

        updateAdvancedSequenceProgress();
    }

    function updateAdvancedSequenceProgress() {
        const placedNumbers = document.querySelectorAll('.placed-advanced-number').length;
        const placedCount = document.getElementById('advanced-placed-count');
        const progressFill = document.getElementById('advanced-sequence-progress-fill');

        if (placedCount) {
            placedCount.textContent = placedNumbers;
        }

        if (progressFill) {
            const percentage = (placedNumbers / totalNumbers) * 100;
            progressFill.style.width = percentage + '%';
        }

        // فحص تلقائي عند اكتمال جميع الأرقام
        if (placedNumbers === totalNumbers) {
            setTimeout(() => {
                checkAdvancedSequenceAutomatically();
            }, 500);
        }
    }

    function checkAdvancedSequenceAutomatically() {
        let correctCount = 0;
        const slots = document.querySelectorAll('.advanced-sequence-slot');

        slots.forEach(slot => {
            const placedNumber = slot.querySelector('.placed-advanced-number');
            if (placedNumber) {
                const numberValue = parseInt(placedNumber.dataset.number);
                const slotPosition = parseInt(slot.dataset.position);

                if (numberValue === slotPosition) {
                    correctCount++;
                    slot.classList.add('final-correct');
                    recordAnswer(true);
                } else {
                    slot.classList.add('final-incorrect');
                    recordAnswer(false);
                }
            }
        });

        if (correctCount === totalNumbers) {
            // ترتيب صحيح كامل
            enhancedSpeakLevel2('ممتاز! رتبت جميع الأرقام بشكل صحيح!', 'ar', 'excellent.mp3');
            celebrateGameCompletion();

            setTimeout(() => {
                nextGame();
            }, 3000);
        } else {
            // ترتيب غير مكتمل
            enhancedSpeakLevel2(`أحسنت! ${correctCount} أرقام في المكان الصحيح من ${totalNumbers}`, 'ar', 'good.mp3');

            setTimeout(() => {
                // إعادة تعيين الأرقام الخاطئة
                resetIncorrectAdvancedNumbers();
            }, 2000);
        }
    }

    function resetIncorrectAdvancedNumbers() {
        const incorrectSlots = document.querySelectorAll('.advanced-sequence-slot.final-incorrect');
        incorrectSlots.forEach(slot => {
            const placedNumber = slot.querySelector('.placed-advanced-number');
            if (placedNumber) {
                returnAdvancedNumberToPool(placedNumber);
            }
        });
    }

    function showCountingDisplay(number) {
        const countingDisplay = document.getElementById('counting-display');
        const countingItems = countingDisplay.querySelector('.counting-items');
        const countingText = countingDisplay.querySelector('.counting-text');

        // إنشاء عناصر العد
        countingItems.innerHTML = '';
        for (let i = 1; i <= number; i++) {
            const item = document.createElement('div');
            item.className = 'counting-item';
            item.textContent = '⭐';
            item.style.animationDelay = `${i * 0.1}s`;
            countingItems.appendChild(item);
        }

        countingText.textContent = `${number} نجوم`;

        // تأثير العد
        setTimeout(() => {
            enhancedSpeakLevel2(`Count: ${number}`, 'en', `count_${number}_en.mp3`);
        }, 500);
    }

    showAdvancedNumbersGame();
}

// وظائف مساعدة إضافية
function celebrateGameCompletion() {
    // تأثير الاحتفال
    const gameContent = document.getElementById('game-content');
    if (gameContent) {
        gameContent.style.animation = 'celebrate 1s ease-in-out';
        setTimeout(() => {
            gameContent.style.animation = '';
        }, 1000);
    }
}

// إضافة تأثير الاحتفال في CSS
const celebrateStyle = document.createElement('style');
celebrateStyle.textContent = `
    @keyframes celebrate {
        0%, 100% { transform: scale(1); }
        25% { transform: scale(1.02); }
        50% { transform: scale(1.05); }
        75% { transform: scale(1.02); }
    }
`;
document.head.appendChild(celebrateStyle);

// لعبة الألوان المتقدمة
function loadAdvancedColorsGame() {
    let selectedColor = null;
    let matchedPairs = 0;
    const totalPairs = advancedColorsData.length;
    const shuffledColors = shuffleArray([...advancedColorsData]);

    function showAdvancedColorsGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-colors-game">
                <h2>🌈 لعبة الألوان المتقدمة</h2>
                <p class="game-instruction">انقر على اللون ثم انقر على الشيء الذي يحمل نفس اللون</p>
                <p class="game-instruction-en">Click on a color then click on an item with the same color</p>

                <div class="advanced-colors-container">
                    <div class="colors-palette">
                        <h3>الألوان</h3>
                        <div class="clickable-colors">
                            ${shuffledColors.map(color => `
                                <div class="clickable-color"
                                     data-color="${color.color}"
                                     style="background-color: ${color.hex}">
                                    <span class="color-name">${color.color}</span>
                                    <span class="color-emoji">${color.emoji}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="color-items">
                        <h3>الأشياء</h3>
                        <div class="clickable-items">
                            ${shuffledColors.map(color =>
                                color.items.map(item => `
                                    <div class="clickable-item"
                                         data-color="${color.color}"
                                         data-item="${item}">
                                        <div class="item-image">
                                            <img src="images/level2/${color.color.toLowerCase().replace(' ', '_')}_${item.toLowerCase().replace(' ', '_')}.png"
                                                 alt="${item}"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div class="item-fallback" style="display: none; background-color: ${color.hex}">
                                                ${color.emoji}
                                            </div>
                                        </div>
                                        <span class="item-name">${item}</span>
                                    </div>
                                `).join('')
                            ).join('')}
                        </div>
                    </div>
                </div>

                <div class="color-matching-progress">
                    <div class="progress-text">الألوان المطابقة: <span id="matched-colors-count">0</span> من ${totalPairs}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="color-matching-progress-fill" style="width: 0%"></div>
                    </div>
                </div>

                <div class="color-learning-section">
                    <h3>تعلم الألوان</h3>
                    <div class="color-display" id="color-display">
                        <div class="color-preview"></div>
                        <div class="color-info">انقر على لون لتعلم المزيد عنه</div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedColorsClickEvents();
    }

    function setupAdvancedColorsClickEvents() {
        const clickableColors = document.querySelectorAll('.clickable-color');
        const clickableItems = document.querySelectorAll('.clickable-item');

        // إعداد الألوان القابلة للنقر
        clickableColors.forEach(color => {
            color.addEventListener('click', handleColorClick);
            color.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleColorClick.call(this, e);
            });
        });

        // إعداد الأشياء القابلة للنقر
        clickableItems.forEach(item => {
            item.addEventListener('click', handleItemClick);
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleItemClick.call(this, e);
            });
        });
    }

    function handleColorClick(e) {
        e.preventDefault();

        // إلغاء تحديد اللون السابق
        if (selectedColor) {
            selectedColor.classList.remove('selected');
        }

        // تحديد اللون الجديد
        selectedColor = this;
        selectedColor.classList.add('selected');

        // نطق اسم اللون بالإنجليزية
        const colorName = selectedColor.dataset.color;
        enhancedSpeakLevel2(colorName, 'en', `${colorName.toLowerCase().replace(' ', '_')}_en.mp3`);

        // عرض معلومات اللون
        showColorInfo(colorName);

        // تأثير بصري
        selectedColor.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedColor) {
                selectedColor.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handleItemClick(e) {
        e.preventDefault();

        if (!selectedColor) {
            enhancedSpeakLevel2('اختر لوناً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للألوان
            const colorsArea = document.querySelector('.colors-palette');
            if (colorsArea) {
                colorsArea.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    colorsArea.style.animation = '';
                }, 600);
            }
            return;
        }

        const item = this;
        checkColorMatch(selectedColor, item);
    }

    function checkColorMatch(colorElement, itemElement) {
        const selectedColorName = colorElement.dataset.color;
        const itemColorName = itemElement.dataset.color;
        const itemName = itemElement.dataset.item;

        if (selectedColorName === itemColorName) {
            // مطابقة صحيحة
            handleCorrectColorMatch(colorElement, itemElement);
        } else {
            // مطابقة خاطئة
            handleIncorrectColorMatch(itemElement);
        }
    }

    function handleCorrectColorMatch(colorElement, itemElement) {
        // تأثيرات بصرية للنجاح
        colorElement.classList.add('matched');
        itemElement.classList.add('matched');

        // إخفاء العناصر المطابقة
        setTimeout(() => {
            colorElement.style.display = 'none';
            itemElement.style.display = 'none';
        }, 1000);

        // تسجيل النقاط والتقدم
        matchedPairs++;
        recordAnswer(true);
        updateColorMatchingProgress();

        // تأثيرات صوتية
        enhancedSpeakLevel2('ممتاز! مطابقة صحيحة!', 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        // إلغاء التحديد
        selectedColor = null;

        // التحقق من اكتمال اللعبة
        if (matchedPairs >= totalPairs) {
            setTimeout(() => {
                completeAdvancedColorsGame();
            }, 1500);
        }
    }

    function handleIncorrectColorMatch(itemElement) {
        // تأثير اهتزاز للعنصر
        itemElement.classList.add('shake-animation');
        enhancedSpeakLevel2('حاول مرة أخرى', 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            itemElement.classList.remove('shake-animation');
            // إلغاء التحديد بعد المحاولة الخاطئة
            if (selectedColor) {
                selectedColor.classList.remove('selected');
                selectedColor = null;
            }
        }, 600);
    }

    function showColorInfo(colorName) {
        const colorDisplay = document.getElementById('color-display');
        const colorPreview = colorDisplay.querySelector('.color-preview');
        const colorInfo = colorDisplay.querySelector('.color-info');

        const colorData = advancedColorsData.find(c => c.color === colorName);
        if (colorData) {
            colorPreview.style.backgroundColor = colorData.hex;
            colorPreview.textContent = colorData.emoji;
            colorInfo.innerHTML = `
                <strong>${colorData.color}</strong><br>
                <span style="color: #6c757d;">${colorData.arabic}</span><br>
                <small>أشياء بهذا اللون: ${colorData.items.join(', ')}</small>
            `;
        }
    }

    function updateColorMatchingProgress() {
        const matchedCount = document.getElementById('matched-colors-count');
        const progressFill = document.getElementById('color-matching-progress-fill');

        if (matchedCount) {
            matchedCount.textContent = matchedPairs;
        }

        if (progressFill) {
            const percentage = (matchedPairs / totalPairs) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeAdvancedColorsGame() {
        enhancedSpeakLevel2('رائع! أكملت لعبة الألوان المتقدمة!', 'ar', 'level_complete.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showAdvancedColorsGame();
}

// لعبة البيت والمفردات اليومية
function loadHouseVocabularyGame() {
    let selectedRoom = null;
    let exploredRooms = 0;
    let currentQuestionIndex = 0;
    let correctAnswers = 0;
    const totalRooms = houseVocabularyData.length;
    let gamePhase = 'exploration'; // exploration أو practice

    // أسئلة التمرين
    const practiceQuestions = [
        {
            questionAr: "ما هي الغرفة التي ننام بها؟",
            questionEn: "Which room do we sleep in?",
            correctAnswer: "Bedroom",
            options: ["Kitchen", "Bedroom", "Living Room"]
        },
        {
            questionAr: "أين نطبخ الطعام؟",
            questionEn: "Where do we cook food?",
            correctAnswer: "Kitchen",
            options: ["Bedroom", "Kitchen", "Window"]
        },
        {
            questionAr: "ما الذي نفتحه ونغلقه للدخول والخروج؟",
            questionEn: "What do we open and close to enter and exit?",
            correctAnswer: "Door",
            options: ["Window", "Table", "Door"]
        },
        {
            questionAr: "أين نجلس لمشاهدة التلفزيون؟",
            questionEn: "Where do we sit to watch TV?",
            correctAnswer: "Living Room",
            options: ["Living Room", "Kitchen", "Door"]
        },
        {
            questionAr: "ما الذي ننظر من خلاله إلى الخارج؟",
            questionEn: "What do we look through to see outside?",
            correctAnswer: "Window",
            options: ["Door", "Window", "Table"]
        },
        {
            questionAr: "أين نضع الطعام لتناوله؟",
            questionEn: "Where do we put food to eat it?",
            correctAnswer: "Table",
            options: ["Window", "Door", "Table"]
        }
    ];

    function showHouseVocabularyGame() {
        const gameContent = document.getElementById('game-content');

        if (gamePhase === 'exploration') {
            gameContent.innerHTML = `
                <div class="house-vocabulary-game">
                    <h2>🏠 لعبة البيت والمفردات اليومية</h2>
                    <p class="game-instruction">انقر على أجزاء البيت لاستكشافها وتعلم أسمائها</p>
                    <p class="game-instruction-en">Click on house parts to explore and learn their names</p>

                    <div class="house-container">
                        <div class="interactive-house">
                            <div class="house-structure">
                                ${houseVocabularyData.map((room, index) => `
                                    <div class="house-room ${room.word.toLowerCase().replace(' ', '-')}"
                                         data-room="${room.word}"
                                         data-arabic="${room.arabic}"
                                         data-description="${room.description}"
                                         data-index="${index}">
                                        <div class="room-icon">${room.emoji}</div>
                                        <div class="room-label">${room.word}</div>
                                        <div class="room-arabic">${room.arabic}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="vocabulary-info">
                            <h3>معلومات المكان</h3>
                            <div class="room-details" id="room-details">
                                <div class="room-preview">
                                    <div class="preview-icon">🏠</div>
                                    <div class="preview-text">انقر على مكان في البيت لاستكشافه</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="exploration-progress">
                        <div class="progress-text">الأماكن المستكشفة: <span id="explored-count">0</span> من ${totalRooms}</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="house-progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            showPracticePhase();
        }

        setupHouseVocabularyEvents();
    }

    function showPracticePhase() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="house-vocabulary-game">
                <h2>🏠 تمرين مفردات البيت</h2>
                <p class="game-instruction">أجب على الأسئلة التالية</p>
                <p class="game-instruction-en">Answer the following questions</p>

                <div class="practice-container">
                    <div class="question-area">
                        <div class="question-display" id="question-display">
                            <!-- سيتم تحديث المحتوى ديناميكياً -->
                        </div>

                        <div class="answer-options" id="answer-options">
                            <!-- سيتم تحديث المحتوى ديناميكياً -->
                        </div>
                    </div>
                </div>

                <div class="practice-progress">
                    <div class="progress-text">الأسئلة المجابة: <span id="practice-count">0</span> من ${practiceQuestions.length}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="practice-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        showCurrentQuestion();
    }

    function setupHouseVocabularyEvents() {
        if (gamePhase === 'exploration') {
            const houseRooms = document.querySelectorAll('.house-room');

            houseRooms.forEach(room => {
                room.addEventListener('click', handleRoomClick);
                room.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    handleRoomClick.call(this, e);
                });
            });
        }
    }

    function handleRoomClick(e) {
        e.preventDefault();

        const room = this;
        const roomName = room.dataset.room;
        const roomArabic = room.dataset.arabic;
        const roomDescription = room.dataset.description;

        // تحديد المكان
        if (selectedRoom) {
            selectedRoom.classList.remove('selected');
        }
        selectedRoom = room;
        selectedRoom.classList.add('selected');

        // نطق الاسم بالعربية ثم بالإنجليزية
        enhancedSpeakLevel2(roomArabic, 'ar', `${roomName.toLowerCase().replace(' ', '_')}_ar.mp3`);
        setTimeout(() => {
            enhancedSpeakLevel2(roomName, 'en', `${roomName.toLowerCase().replace(' ', '_')}_en.mp3`);
        }, 1500);

        // عرض تفاصيل المكان
        showRoomDetails(roomName, roomArabic, roomDescription);

        // تأثير بصري
        room.style.transform = 'scale(1.1)';
        setTimeout(() => {
            room.style.transform = 'scale(1)';
        }, 300);

        // تحديث التقدم إذا لم يكن مستكشفاً من قبل
        if (!room.classList.contains('explored')) {
            room.classList.add('explored');
            exploredRooms++;
            updateExplorationProgress();
        }

        // بدء التمرين بعد استكشاف جميع الأماكن
        if (exploredRooms >= totalRooms) {
            setTimeout(() => {
                startPracticePhase();
            }, 3000);
        }
    }

    function showRoomDetails(roomName, roomArabic, roomDescription) {
        const roomDetails = document.getElementById('room-details');
        const roomData = houseVocabularyData.find(r => r.word === roomName);

        if (roomDetails && roomData) {
            roomDetails.innerHTML = `
                <div class="room-preview">
                    <div class="preview-icon">${roomData.emoji}</div>
                    <div class="preview-info">
                        <h4>${roomName}</h4>
                        <div class="arabic-name">${roomArabic}</div>
                        <div class="room-desc">${roomDescription}</div>
                        <button class="speak-again-btn" onclick="repeatRoomInfo('${roomName}', '${roomArabic}')">
                            🔊 أعد النطق
                        </button>
                    </div>
                </div>
            `;
        }
    }

    window.repeatRoomInfo = function(roomName, roomArabic) {
        enhancedSpeakLevel2(roomArabic, 'ar', `${roomName.toLowerCase().replace(' ', '_')}_ar.mp3`);
        setTimeout(() => {
            enhancedSpeakLevel2(roomName, 'en', `${roomName.toLowerCase().replace(' ', '_')}_en.mp3`);
        }, 1500);
    };

    function startPracticePhase() {
        enhancedSpeakLevel2('ممتاز! الآن دعنا نختبر ما تعلمته', 'ar', 'start_practice.mp3');

        setTimeout(() => {
            gamePhase = 'practice';
            currentQuestionIndex = 0;
            correctAnswers = 0;
            showHouseVocabularyGame();
        }, 2000);
    }

    function showCurrentQuestion() {
        if (currentQuestionIndex >= practiceQuestions.length) {
            completeHouseVocabularyGame();
            return;
        }

        const question = practiceQuestions[currentQuestionIndex];
        const questionDisplay = document.getElementById('question-display');
        const answerOptions = document.getElementById('answer-options');

        questionDisplay.innerHTML = `
            <div class="question-content">
                <div class="question-text-ar">
                    <span class="question-label">السؤال:</span>
                    <span class="question-ar">${question.questionAr}</span>
                    <button class="speak-question-btn" onclick="speakQuestion('ar', '${question.questionAr}')">🔊</button>
                </div>
                <div class="question-text-en">
                    <span class="question-label">Question:</span>
                    <span class="question-en">${question.questionEn}</span>
                    <button class="speak-question-btn" onclick="speakQuestion('en', '${question.questionEn}')">🔊</button>
                </div>
            </div>
        `;

        answerOptions.innerHTML = `
            <div class="options-grid">
                ${question.options.map((option, index) => `
                    <button class="answer-option"
                            data-answer="${option}"
                            onclick="selectAnswer('${option}', '${question.correctAnswer}')">
                        ${option}
                    </button>
                `).join('')}
            </div>
        `;
    }

    window.speakQuestion = function(language, text) {
        if (language === 'ar') {
            enhancedSpeakLevel2(text, 'ar', `question_${currentQuestionIndex}_ar.mp3`);
        } else {
            enhancedSpeakLevel2(text, 'en', `question_${currentQuestionIndex}_en.mp3`);
        }
    };

    window.selectAnswer = function(selectedAnswer, correctAnswer) {
        const options = document.querySelectorAll('.answer-option');

        // تعطيل جميع الخيارات
        options.forEach(option => {
            option.disabled = true;

            if (option.dataset.answer === correctAnswer) {
                option.classList.add('correct');
            } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== correctAnswer) {
                option.classList.add('incorrect');
            }
        });

        if (selectedAnswer === correctAnswer) {
            correctAnswers++;
            enhancedSpeakLevel2('إجابة صحيحة! ممتاز!', 'ar', 'correct_answer.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeakLevel2('حاول مرة أخرى في المرة القادمة', 'ar', 'try_again.mp3');
        }

        // الانتقال للسؤال التالي
        setTimeout(() => {
            currentQuestionIndex++;
            updatePracticeProgress();
            showCurrentQuestion();
        }, 2000);
    };

    function updateExplorationProgress() {
        const exploredCount = document.getElementById('explored-count');
        const progressFill = document.getElementById('house-progress-fill');

        if (exploredCount) {
            exploredCount.textContent = exploredRooms;
        }

        if (progressFill) {
            const percentage = (exploredRooms / totalRooms) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function updatePracticeProgress() {
        const practiceCount = document.getElementById('practice-count');
        const progressFill = document.getElementById('practice-progress-fill');

        if (practiceCount) {
            practiceCount.textContent = currentQuestionIndex;
        }

        if (progressFill) {
            const percentage = (currentQuestionIndex / practiceQuestions.length) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeHouseVocabularyGame() {
        const accuracy = Math.round((correctAnswers / practiceQuestions.length) * 100);

        enhancedSpeakLevel2(`ممتاز! أكملت لعبة البيت بنسبة ${accuracy}%`, 'ar', 'house_game_complete.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            // الانتقال للعبة التالية
            loadClothesGame();
        }, 3000);
    }



    showHouseVocabularyGame();
}

// تم حذف لعبة الملابس

// لعبة الطعام المتقدمة (تم نقلها لتحل محل لعبة الملابس)
function loadClothesGame() {
    let selectedIngredient = null;
    let cookedMeals = 0;
    const totalMeals = 3; // عدد الوجبات المختلفة
    const meals = [
        { name: 'Breakfast', ingredients: ['Egg', 'Bread'], emoji: '🍳', arabic: 'فطار' },
        { name: 'Lunch', ingredients: ['Rice', 'Cheese'], emoji: '🍽️', arabic: 'غداء' },
        { name: 'Dinner', ingredients: ['Soup', 'Orange'], emoji: '🍲', arabic: 'عشاء' }
    ];
    let currentMeal = 0;

    function showAdvancedFoodGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-food-game">
                <h2>🍊 لعبة الطعام المتقدمة</h2>
                <p class="game-instruction">اطبخ الوجبات باستخدام المكونات الصحيحة</p>
                <p class="game-instruction-en">Cook meals using the correct ingredients</p>

                <div class="cooking-container">
                    <div class="ingredients-area">
                        <h3>المكونات</h3>
                        <div class="ingredients-grid">
                            ${advancedFoodData.map(food => `
                                <div class="ingredient-item"
                                     data-ingredient="${food.word}"
                                     data-category="${food.category}"
                                     data-taste="${food.taste}">
                                    <div class="ingredient-icon">${food.emoji}</div>
                                    <div class="ingredient-name">${food.word}</div>
                                    <div class="ingredient-arabic">${food.arabic}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="cooking-area">
                        <h3>منطقة الطبخ</h3>
                        <div class="current-meal" id="current-meal">
                            <div class="meal-display">
                                <div class="meal-icon">${meals[currentMeal].emoji}</div>
                                <div class="meal-info">
                                    <h4>${meals[currentMeal].name}</h4>
                                    <p class="meal-arabic">${meals[currentMeal].arabic}</p>
                                    <p class="meal-ingredients">المكونات المطلوبة: ${meals[currentMeal].ingredients.join(', ')}</p>
                                </div>
                            </div>
                            <div class="cooking-pot" id="cooking-pot">
                                <div class="pot-icon">🍳</div>
                                <div class="pot-contents" id="pot-contents"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="food-learning">
                    <h3>تعلم الطعام</h3>
                    <div class="food-info" id="food-info">
                        <div class="info-display">
                            <div class="info-icon">🍊</div>
                            <div class="info-text">انقر على مكون لتعلم اسمه</div>
                        </div>
                    </div>
                </div>

                <div class="cooking-progress">
                    <div class="progress-text">الوجبات المطبوخة: <span id="cooked-count">0</span> من ${totalMeals}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="food-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedFoodEvents();
    }

    function setupAdvancedFoodEvents() {
        const ingredientItems = document.querySelectorAll('.ingredient-item');
        const cookingPot = document.getElementById('cooking-pot');

        ingredientItems.forEach(item => {
            item.addEventListener('click', handleIngredientClick);
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleIngredientClick.call(this, e);
            });
        });

        if (cookingPot) {
            cookingPot.addEventListener('click', handlePotClick);
            cookingPot.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handlePotClick.call(this, e);
            });
        }
    }

    function handleIngredientClick(e) {
        e.preventDefault();

        // إلغاء تحديد المكون السابق
        if (selectedIngredient) {
            selectedIngredient.classList.remove('selected');
        }

        // تحديد المكون الجديد
        selectedIngredient = this;
        selectedIngredient.classList.add('selected');

        // نطق اسم المكون بالإنجليزية
        const ingredientName = selectedIngredient.dataset.ingredient;
        enhancedSpeakLevel2(ingredientName, 'en', `${ingredientName.toLowerCase()}_en.mp3`);

        // عرض معلومات المكون
        showFoodInfo(ingredientName);

        // تأثير بصري
        selectedIngredient.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedIngredient) {
                selectedIngredient.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handlePotClick(e) {
        e.preventDefault();

        if (!selectedIngredient) {
            enhancedSpeakLevel2('اختر مكوناً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للمكونات
            const ingredientsArea = document.querySelector('.ingredients-area');
            if (ingredientsArea) {
                ingredientsArea.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    ingredientsArea.style.animation = '';
                }, 600);
            }
            return;
        }

        cookIngredient(selectedIngredient);
    }

    function cookIngredient(ingredientElement) {
        const ingredientName = ingredientElement.dataset.ingredient;
        const currentMealData = meals[currentMeal];

        // التحقق من صحة المكون للوجبة الحالية
        if (currentMealData.ingredients.includes(ingredientName)) {
            // مكون صحيح
            handleCorrectIngredient(ingredientElement, ingredientName);
        } else {
            // مكون خاطئ
            handleIncorrectIngredient(ingredientElement);
        }
    }

    function handleCorrectIngredient(ingredientElement, ingredientName) {
        const potContents = document.getElementById('pot-contents');
        const ingredientEmoji = ingredientElement.querySelector('.ingredient-icon').textContent;

        // إضافة المكون للوعاء
        const ingredientInPot = document.createElement('div');
        ingredientInPot.className = 'pot-ingredient';
        ingredientInPot.innerHTML = `
            <span class="pot-emoji">${ingredientEmoji}</span>
            <span class="pot-name">${ingredientName}</span>
        `;
        potContents.appendChild(ingredientInPot);

        // إخفاء المكون من المكونات المتاحة
        ingredientElement.style.display = 'none';

        // إلغاء التحديد
        selectedIngredient = null;

        // تأثيرات بصرية وصوتية
        const cookingPot = document.getElementById('cooking-pot');
        cookingPot.classList.add('cooking-animation');
        enhancedSpeakLevel2('ممتاز! مكون صحيح!', 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        setTimeout(() => {
            cookingPot.classList.remove('cooking-animation');
        }, 1000);

        // التحقق من اكتمال الوجبة
        const currentMealData = meals[currentMeal];
        const addedIngredients = Array.from(potContents.children).map(child =>
            child.querySelector('.pot-name').textContent
        );

        if (addedIngredients.length >= currentMealData.ingredients.length) {
            setTimeout(() => {
                completeMeal();
            }, 1500);
        }
    }

    function handleIncorrectIngredient(ingredientElement) {
        // تأثير اهتزاز للوعاء
        const cookingPot = document.getElementById('cooking-pot');
        cookingPot.classList.add('shake-animation');
        enhancedSpeakLevel2('هذا المكون لا يناسب هذه الوجبة', 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            cookingPot.classList.remove('shake-animation');
            // إلغاء التحديد بعد المحاولة الخاطئة
            if (selectedIngredient) {
                selectedIngredient.classList.remove('selected');
                selectedIngredient = null;
            }
        }, 600);
    }

    function showFoodInfo(ingredientName) {
        const foodInfo = document.getElementById('food-info');
        const foodData = advancedFoodData.find(f => f.word === ingredientName);

        if (foodData) {
            foodInfo.innerHTML = `
                <div class="info-display">
                    <div class="info-icon">${foodData.emoji}</div>
                    <div class="info-details">
                        <h4>${foodData.word}</h4>
                        <p class="arabic-name">${foodData.arabic}</p>
                        <p class="food-category">Category: ${foodData.category}</p>
                        <p class="food-taste">Taste: ${foodData.taste}</p>
                        <button class="speak-again-btn" onclick="enhancedSpeakLevel2('${foodData.word}', 'en', '${foodData.word.toLowerCase()}_en.mp3')">
                            🔊 نطق مرة أخرى
                        </button>
                    </div>
                </div>
            `;
        }
    }

    function completeMeal() {
        const currentMealData = meals[currentMeal];
        enhancedSpeakLevel2(`رائع! طبخت ${currentMealData.arabic}!`, 'ar', 'meal_complete.mp3');

        // تأثيرات الاحتفال
        celebrateGameCompletion();

        // تحديث التقدم
        cookedMeals++;
        updateCookingProgress();

        // الانتقال للوجبة التالية أو إنهاء اللعبة
        setTimeout(() => {
            if (currentMeal < totalMeals - 1) {
                currentMeal++;
                resetForNextMeal();
            } else {
                completeAdvancedFoodGame();
            }
        }, 2000);
    }

    function resetForNextMeal() {
        // إعادة تعيين الوعاء
        const potContents = document.getElementById('pot-contents');
        potContents.innerHTML = '';

        // إعادة إظهار جميع المكونات
        const ingredientItems = document.querySelectorAll('.ingredient-item');
        ingredientItems.forEach(item => {
            item.style.display = 'block';
            item.classList.remove('selected');
        });

        // إلغاء التحديد
        selectedIngredient = null;

        // تحديث عرض الوجبة الحالية
        const currentMealDisplay = document.getElementById('current-meal');
        const currentMealData = meals[currentMeal];

        currentMealDisplay.querySelector('.meal-icon').textContent = currentMealData.emoji;
        currentMealDisplay.querySelector('h4').textContent = currentMealData.name;
        currentMealDisplay.querySelector('.meal-arabic').textContent = currentMealData.arabic;
        currentMealDisplay.querySelector('.meal-ingredients').textContent =
            `المكونات المطلوبة: ${currentMealData.ingredients.join(', ')}`;

        enhancedSpeakLevel2(`الآن اطبخ ${currentMealData.arabic}`, 'ar', 'next_meal.mp3');
    }

    function updateCookingProgress() {
        const cookedCount = document.getElementById('cooked-count');
        const progressFill = document.getElementById('food-progress-fill');

        if (cookedCount) {
            cookedCount.textContent = cookedMeals;
        }

        if (progressFill) {
            const percentage = (cookedMeals / totalMeals) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeAdvancedFoodGame() {
        enhancedSpeakLevel2('ممتاز! طبخت جميع الوجبات!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            loadActivitiesGame();
        }, 3000);
    }

    showAdvancedFoodGame();
}


// لعبة الهوايات والأنشطة
function loadActivitiesGame() {
    let selectedActivity = null;
    let completedActivities = 0;
    const totalActivities = activitiesData.length;
    const shuffledActivities = shuffleArray([...activitiesData]);

    function showActivitiesGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="activities-game">
                <h2>🎨 لعبة الهوايات والأنشطة</h2>
                <p class="game-instruction">اربط كل نشاط بالوقت المناسب له</p>
                <p class="game-instruction-en">Match each activity with its appropriate time</p>

                <div class="activities-container">
                    <div class="activities-area">
                        <h3>الأنشطة</h3>
                        <div class="activities-grid">
                            ${shuffledActivities.map(activity => `
                                <div class="activity-item"
                                     data-activity="${activity.word}"
                                     data-time="${activity.time}"
                                     data-type="${activity.type}">
                                    <div class="activity-icon">${activity.emoji}</div>
                                    <div class="activity-name">${activity.word}</div>
                                    <div class="activity-arabic">${activity.arabic}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="time-slots">
                        <h3>أوقات اليوم</h3>
                        <div class="time-periods">
                            <div class="time-slot" data-time="morning">
                                <div class="time-icon">🌅</div>
                                <div class="time-label">الصباح</div>
                                <div class="time-label-en">Morning</div>
                                <div class="time-activities" id="morning-activities"></div>
                            </div>
                            <div class="time-slot" data-time="afternoon">
                                <div class="time-icon">☀️</div>
                                <div class="time-label">بعد الظهر</div>
                                <div class="time-label-en">Afternoon</div>
                                <div class="time-activities" id="afternoon-activities"></div>
                            </div>
                            <div class="time-slot" data-time="evening">
                                <div class="time-icon">🌆</div>
                                <div class="time-label">المساء</div>
                                <div class="time-label-en">Evening</div>
                                <div class="time-activities" id="evening-activities"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="activity-learning">
                    <h3>تعلم الأنشطة</h3>
                    <div class="activity-info" id="activity-info">
                        <div class="info-display">
                            <div class="info-icon">🎨</div>
                            <div class="info-text">انقر على نشاط لتعلم اسمه</div>
                        </div>
                    </div>
                </div>

                <div class="activities-progress">
                    <div class="progress-text">الأنشطة المكتملة: <span id="activities-count">0</span> من ${totalActivities}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="activities-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupActivitiesEvents();
    }

    function setupActivitiesEvents() {
        const activityItems = document.querySelectorAll('.activity-item');
        const timeSlots = document.querySelectorAll('.time-slot');

        activityItems.forEach(item => {
            item.addEventListener('click', handleActivityClick);
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleActivityClick.call(this, e);
            });
        });

        timeSlots.forEach(slot => {
            slot.addEventListener('click', handleTimeSlotClick);
            slot.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleTimeSlotClick.call(this, e);
            });
        });
    }

    function handleActivityClick(e) {
        e.preventDefault();

        // إلغاء تحديد النشاط السابق
        if (selectedActivity) {
            selectedActivity.classList.remove('selected');
        }

        // تحديد النشاط الجديد
        selectedActivity = this;
        selectedActivity.classList.add('selected');

        // نطق اسم النشاط بالإنجليزية
        const activityName = selectedActivity.dataset.activity;
        enhancedSpeakLevel2(activityName, 'en', `${activityName.toLowerCase()}_en.mp3`);

        // عرض معلومات النشاط
        showActivityInfo(activityName);

        // تأثير بصري
        selectedActivity.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedActivity) {
                selectedActivity.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handleTimeSlotClick(e) {
        e.preventDefault();

        if (!selectedActivity) {
            enhancedSpeakLevel2('اختر نشاطاً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للأنشطة
            const activitiesArea = document.querySelector('.activities-area');
            if (activitiesArea) {
                activitiesArea.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    activitiesArea.style.animation = '';
                }, 600);
            }
            return;
        }

        const timeSlot = this.closest('.time-slot');
        matchActivityToTime(selectedActivity, timeSlot);
    }

    function matchActivityToTime(activityElement, timeSlot) {
        const activityTime = activityElement.dataset.time;
        const slotTime = timeSlot.dataset.time;
        const activityName = activityElement.dataset.activity;

        // التحقق من التطابق
        if (activityTime === slotTime) {
            // تطابق صحيح
            handleCorrectMatch(activityElement, timeSlot);
        } else {
            // تطابق خاطئ
            handleIncorrectMatch(activityElement, timeSlot);
        }
    }

    function handleCorrectMatch(activityElement, timeSlot) {
        const timeActivities = timeSlot.querySelector('.time-activities');
        const activityEmoji = activityElement.querySelector('.activity-icon').textContent;
        const activityName = activityElement.dataset.activity;

        // إضافة النشاط للوقت المناسب
        const activityInSlot = document.createElement('div');
        activityInSlot.className = 'matched-activity';
        activityInSlot.innerHTML = `
            <span class="activity-emoji">${activityEmoji}</span>
            <span class="activity-name">${activityName}</span>
        `;
        timeActivities.appendChild(activityInSlot);

        // إخفاء النشاط من الأنشطة المتاحة
        activityElement.style.display = 'none';

        // إلغاء التحديد
        selectedActivity = null;

        // تأثيرات بصرية وصوتية
        timeSlot.classList.add('correct-match');
        enhancedSpeakLevel2('ممتاز! تطابق صحيح!', 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        setTimeout(() => {
            timeSlot.classList.remove('correct-match');
        }, 1000);

        // تحديث التقدم
        completedActivities++;
        updateActivitiesProgress();

        // التحقق من اكتمال اللعبة
        if (completedActivities >= totalActivities) {
            setTimeout(() => {
                completeActivitiesGame();
            }, 1500);
        }
    }

    function handleIncorrectMatch(activityElement, timeSlot) {
        // تأثير اهتزاز للوقت
        timeSlot.classList.add('shake-animation');
        enhancedSpeakLevel2('هذا النشاط لا يناسب هذا الوقت', 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            timeSlot.classList.remove('shake-animation');
            // إلغاء التحديد بعد المحاولة الخاطئة
            if (selectedActivity) {
                selectedActivity.classList.remove('selected');
                selectedActivity = null;
            }
        }, 600);
    }

    function showActivityInfo(activityName) {
        const activityInfo = document.getElementById('activity-info');
        const activityData = activitiesData.find(a => a.word === activityName);

        if (activityData) {
            activityInfo.innerHTML = `
                <div class="info-display">
                    <div class="info-icon">${activityData.emoji}</div>
                    <div class="info-details">
                        <h4>${activityData.word}</h4>
                        <p class="arabic-name">${activityData.arabic}</p>
                        <p class="activity-time">Best time: ${activityData.time}</p>
                        <p class="activity-type">Type: ${activityData.type}</p>
                        <button class="speak-again-btn" onclick="enhancedSpeakLevel2('${activityData.word}', 'en', '${activityData.word.toLowerCase()}_en.mp3')">
                            🔊 نطق مرة أخرى
                        </button>
                    </div>
                </div>
            `;
        }
    }

    function updateActivitiesProgress() {
        const activitiesCount = document.getElementById('activities-count');
        const progressFill = document.getElementById('activities-progress-fill');

        if (activitiesCount) {
            activitiesCount.textContent = completedActivities;
        }

        if (progressFill) {
            const percentage = (completedActivities / totalActivities) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeActivitiesGame() {
        enhancedSpeakLevel2('ممتاز! ربطت جميع الأنشطة بأوقاتها!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            loadAdvancedAnimalsGame();
        }, 3000);
    }

    showActivitiesGame();
}

// لعبة الحيوانات المتقدمة
function loadAdvancedAnimalsGame() {
    let selectedAnimal = null;
    let fedAnimals = 0;
    const totalAnimals = advancedAnimalsData.length;
    const shuffledAnimals = shuffleArray([...advancedAnimalsData]);

    function showAdvancedAnimalsGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-animals-game">
                <h2>🦁 لعبة الحيوانات المتقدمة</h2>
                <p class="game-instruction">أطعم الحيوانات طعامها المفضل</p>
                <p class="game-instruction-en">Feed the animals their favorite food</p>

                <div class="zoo-container">
                    <div class="animals-area">
                        <h3>الحيوانات</h3>
                        <div class="animals-grid">
                            ${shuffledAnimals.map(animal => `
                                <div class="animal-item"
                                     data-animal="${animal.word}"
                                     data-food="${animal.food}"
                                     data-habitat="${animal.habitat}">
                                    <div class="animal-icon">${animal.emoji}</div>
                                    <div class="animal-name">${animal.word}</div>
                                    <div class="animal-arabic">${animal.arabic}</div>
                                    <div class="animal-hunger" id="hunger-${animal.word}">🍽️</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="food-area">
                        <h3>الطعام</h3>
                        <div class="food-items">
                            ${getUniqueFoods().map(food => `
                                <div class="food-item" data-food="${food}">
                                    <div class="food-icon">${getFoodEmoji(food)}</div>
                                    <div class="food-name">${food}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="animal-learning">
                    <h3>تعلم الحيوانات</h3>
                    <div class="animal-info" id="animal-info">
                        <div class="info-display">
                            <div class="info-icon">🦁</div>
                            <div class="info-text">انقر على حيوان لتعلم اسمه</div>
                        </div>
                    </div>
                </div>

                <div class="feeding-progress">
                    <div class="progress-text">الحيوانات المطعمة: <span id="fed-count">0</span> من ${totalAnimals}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="animals-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedAnimalsEvents();
    }

    function getUniqueFoods() {
        const foods = advancedAnimalsData.map(animal => animal.food);
        return [...new Set(foods)];
    }

    function getFoodEmoji(food) {
        const foodEmojis = {
            'Meat': '🥩',
            'Fish': '🐟',
            'Grass': '🌱',
            'Nuts': '🥜',
            'Fruits': '🍎',
            'Insects': '🐛'
        };
        return foodEmojis[food] || '🍽️';
    }

    function setupAdvancedAnimalsEvents() {
        const animalItems = document.querySelectorAll('.animal-item');
        const foodItems = document.querySelectorAll('.food-item');

        animalItems.forEach(item => {
            item.addEventListener('click', handleAnimalClick);
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleAnimalClick.call(this, e);
            });
        });

        foodItems.forEach(item => {
            item.addEventListener('click', handleFoodClick);
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleFoodClick.call(this, e);
            });
        });
    }

    function handleAnimalClick(e) {
        e.preventDefault();

        // إلغاء تحديد الحيوان السابق
        if (selectedAnimal) {
            selectedAnimal.classList.remove('selected');
        }

        // تحديد الحيوان الجديد
        selectedAnimal = this;
        selectedAnimal.classList.add('selected');

        // نطق اسم الحيوان بالإنجليزية
        const animalName = selectedAnimal.dataset.animal;
        enhancedSpeakLevel2(animalName, 'en', `${animalName.toLowerCase()}_en.mp3`);

        // عرض معلومات الحيوان
        showAnimalInfo(animalName);

        // تأثير بصري
        selectedAnimal.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedAnimal) {
                selectedAnimal.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handleFoodClick(e) {
        e.preventDefault();

        if (!selectedAnimal) {
            enhancedSpeakLevel2('اختر حيواناً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للحيوانات
            const animalsArea = document.querySelector('.animals-area');
            if (animalsArea) {
                animalsArea.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    animalsArea.style.animation = '';
                }, 600);
            }
            return;
        }

        const foodType = this.dataset.food;
        feedAnimal(selectedAnimal, foodType);
    }

    function feedAnimal(animalElement, foodType) {
        const animalFood = animalElement.dataset.food;
        const animalName = animalElement.dataset.animal;

        // التحقق من صحة الطعام للحيوان
        if (animalFood === foodType) {
            // طعام صحيح
            handleCorrectFeeding(animalElement, foodType);
        } else {
            // طعام خاطئ
            handleIncorrectFeeding(animalElement);
        }
    }

    function handleCorrectFeeding(animalElement, foodType) {
        const animalName = animalElement.dataset.animal;
        const hungerIndicator = document.getElementById(`hunger-${animalName}`);

        // تغيير مؤشر الجوع إلى راضي
        if (hungerIndicator) {
            hungerIndicator.textContent = '😊';
            hungerIndicator.style.color = '#4CAF50';
        }

        // إخفاء الحيوان من القائمة (تم إطعامه)
        animalElement.style.opacity = '0.5';
        animalElement.style.pointerEvents = 'none';

        // إلغاء التحديد
        selectedAnimal = null;

        // تأثيرات بصرية وصوتية
        animalElement.classList.add('fed-animation');
        enhancedSpeakLevel2('ممتاز! الحيوان راضي!', 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        setTimeout(() => {
            animalElement.classList.remove('fed-animation');
        }, 1000);

        // تحديث التقدم
        fedAnimals++;
        updateFeedingProgress();

        // التحقق من اكتمال اللعبة
        if (fedAnimals >= totalAnimals) {
            setTimeout(() => {
                completeAdvancedAnimalsGame();
            }, 1500);
        }
    }

    function handleIncorrectFeeding(animalElement) {
        // تأثير اهتزاز للحيوان
        animalElement.classList.add('shake-animation');
        enhancedSpeakLevel2('هذا الطعام لا يناسب هذا الحيوان', 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            animalElement.classList.remove('shake-animation');
            // إلغاء التحديد بعد المحاولة الخاطئة
            if (selectedAnimal) {
                selectedAnimal.classList.remove('selected');
                selectedAnimal = null;
            }
        }, 600);
    }

    function showAnimalInfo(animalName) {
        const animalInfo = document.getElementById('animal-info');
        const animalData = advancedAnimalsData.find(a => a.word === animalName);

        if (animalData) {
            animalInfo.innerHTML = `
                <div class="info-display">
                    <div class="info-icon">${animalData.emoji}</div>
                    <div class="info-details">
                        <h4>${animalData.word}</h4>
                        <p class="arabic-name">${animalData.arabic}</p>
                        <p class="animal-food">Favorite food: ${animalData.food}</p>
                        <p class="animal-habitat">Habitat: ${animalData.habitat}</p>
                        <button class="speak-again-btn" onclick="enhancedSpeakLevel2('${animalData.word}', 'en', '${animalData.word.toLowerCase()}_en.mp3')">
                            🔊 نطق مرة أخرى
                        </button>
                    </div>
                </div>
            `;
        }
    }

    function updateFeedingProgress() {
        const fedCount = document.getElementById('fed-count');
        const progressFill = document.getElementById('animals-progress-fill');

        if (fedCount) {
            fedCount.textContent = fedAnimals;
        }

        if (progressFill) {
            const percentage = (fedAnimals / totalAnimals) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeAdvancedAnimalsGame() {
        enhancedSpeakLevel2('ممتاز! أطعمت جميع الحيوانات!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            loadGrammarGame();
        }, 3000);
    }

    showAdvancedAnimalsGame();
}

// لعبة القواعد والجمل
function loadGrammarGame() {
    let currentExercise = 0;
    let completedExercises = 0;
    const totalExercises = 4; // ضمائر، أفعال، أسئلة، can statements
    const exercises = [
        'pronouns', 'verbs', 'questions', 'can-statements'
    ];

    function showGrammarGame() {
        const gameContent = document.getElementById('game-content');
        const currentExerciseType = exercises[currentExercise];

        let exerciseContent = '';

        switch(currentExerciseType) {
            case 'pronouns':
                exerciseContent = createPronounsExercise();
                break;
            case 'verbs':
                exerciseContent = createVerbsExercise();
                break;
            case 'questions':
                exerciseContent = createQuestionsExercise();
                break;
            case 'can-statements':
                exerciseContent = createCanStatementsExercise();
                break;
        }

        gameContent.innerHTML = `
            <div class="grammar-game">
                <h2>📚 لعبة القواعد والجمل</h2>
                <p class="game-instruction">تعلم القواعد الأساسية للغة الإنجليزية</p>
                <p class="game-instruction-en">Learn basic English grammar rules</p>

                <div class="exercise-progress">
                    <div class="progress-text">التمرين: <span id="exercise-number">${currentExercise + 1}</span> من ${totalExercises}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="grammar-progress-fill" style="width: ${(currentExercise / totalExercises) * 100}%"></div>
                    </div>
                </div>

                ${exerciseContent}

                <div class="grammar-controls">
                    <button class="next-exercise-btn" id="next-exercise-btn" onclick="nextExercise()" style="display: none;">
                        التمرين التالي ⏭️
                    </button>
                </div>
            </div>
        `;

        setupGrammarEvents();
    }

    function createPronounsExercise() {
        return `
            <div class="exercise-content">
                <h3>الضمائر - Pronouns</h3>
                <p class="exercise-instruction">اربط كل ضمير بالجملة المناسبة</p>

                <div class="pronouns-exercise">
                    <div class="pronouns-list">
                        ${pronounsData.map(pronoun => `
                            <div class="pronoun-item" data-pronoun="${pronoun.pronoun}">
                                <span class="pronoun-text">${pronoun.pronoun}</span>
                                <span class="pronoun-arabic">${pronoun.arabic}</span>
                            </div>
                        `).join('')}
                    </div>

                    <div class="sentences-list">
                        ${pronounsData.map(pronoun => `
                            <div class="sentence-slot" data-pronoun="${pronoun.pronoun}">
                                <span class="sentence-text">_____ ${pronoun.sentence.split(' ').slice(1).join(' ')}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    function createVerbsExercise() {
        return `
            <div class="exercise-content">
                <h3>الأفعال - Verbs</h3>
                <p class="exercise-instruction">اختر الفعل الصحيح لكل جملة</p>

                <div class="verbs-exercise">
                    ${verbsData.map((verb, index) => `
                        <div class="verb-question" data-verb="${verb.verb}">
                            <p class="question-text">I _____ to school every day.</p>
                            <div class="verb-options">
                                <button class="verb-option" data-answer="${verb.verb === 'go'}">${verb.verb}</button>
                                <button class="verb-option" data-answer="${verb.verb !== 'go'}">run</button>
                                <button class="verb-option" data-answer="${verb.verb !== 'go'}">fly</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function createQuestionsExercise() {
        return `
            <div class="exercise-content">
                <h3>الأسئلة - Questions</h3>
                <p class="exercise-instruction">حول الجمل إلى أسئلة</p>

                <div class="questions-exercise">
                    ${questionsData.map(question => `
                        <div class="question-item">
                            <p class="statement">${question.statement}</p>
                            <p class="question-prompt">حول إلى سؤال:</p>
                            <div class="question-options">
                                <button class="question-option" data-correct="true">${question.question}</button>
                                <button class="question-option" data-correct="false">${question.statement}?</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function createCanStatementsExercise() {
        return `
            <div class="exercise-content">
                <h3>جمل Can - Can Statements</h3>
                <p class="exercise-instruction">اختر الجملة الصحيحة</p>

                <div class="can-exercise">
                    ${canStatementsData.map(statement => `
                        <div class="can-item">
                            <p class="can-prompt">عبر عن: "${statement.arabic}"</p>
                            <div class="can-options">
                                <button class="can-option" data-correct="true">${statement.sentence}</button>
                                <button class="can-option" data-correct="false">${statement.negative}</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function setupGrammarEvents() {
        // إعداد أحداث الضمائر
        const pronounItems = document.querySelectorAll('.pronoun-item');
        const sentenceSlots = document.querySelectorAll('.sentence-slot');

        pronounItems.forEach(item => {
            item.addEventListener('click', handlePronounClick);
        });

        sentenceSlots.forEach(slot => {
            slot.addEventListener('click', handleSentenceSlotClick);
        });

        // إعداد أحداث الأفعال
        const verbOptions = document.querySelectorAll('.verb-option');
        verbOptions.forEach(option => {
            option.addEventListener('click', handleVerbOptionClick);
        });

        // إعداد أحداث الأسئلة
        const questionOptions = document.querySelectorAll('.question-option');
        questionOptions.forEach(option => {
            option.addEventListener('click', handleQuestionOptionClick);
        });

        // إعداد أحداث جمل Can
        const canOptions = document.querySelectorAll('.can-option');
        canOptions.forEach(option => {
            option.addEventListener('click', handleCanOptionClick);
        });
    }

    function handlePronounClick(e) {
        // إلغاء تحديد الضمير السابق
        document.querySelectorAll('.pronoun-item').forEach(item => {
            item.classList.remove('selected');
        });

        // تحديد الضمير الجديد
        this.classList.add('selected');
        selectedPronoun = this.dataset.pronoun;
    }

    function handleSentenceSlotClick(e) {
        if (!selectedPronoun) {
            enhancedSpeakLevel2('اختر ضميراً أولاً', 'ar', 'try_again.mp3');
            return;
        }

        const correctPronoun = this.dataset.pronoun;
        if (selectedPronoun === correctPronoun) {
            // إجابة صحيحة
            this.innerHTML = `<span class="completed-sentence">${selectedPronoun} ${this.querySelector('.sentence-text').textContent.replace('_____', '').trim()}</span>`;
            this.classList.add('correct');
            enhancedSpeakLevel2('ممتاز!', 'ar', 'excellent.mp3');
            celebrateCorrectAnswer();

            // إخفاء الضمير المستخدم
            document.querySelector(`[data-pronoun="${selectedPronoun}"]`).style.display = 'none';
            selectedPronoun = null;

            checkExerciseCompletion();
        } else {
            // إجابة خاطئة
            this.classList.add('shake-animation');
            enhancedSpeakLevel2('حاول مرة أخرى', 'ar', 'try_again.mp3');
            recordAnswer(false);

            setTimeout(() => {
                this.classList.remove('shake-animation');
            }, 600);
        }
    }

    function handleVerbOptionClick(e) {
        const isCorrect = this.dataset.answer === 'true';

        if (isCorrect) {
            this.classList.add('correct');
            enhancedSpeakLevel2('ممتاز!', 'ar', 'excellent.mp3');
            celebrateCorrectAnswer();

            // تعطيل جميع الخيارات في هذا السؤال
            const questionDiv = this.closest('.verb-question');
            questionDiv.querySelectorAll('.verb-option').forEach(option => {
                option.disabled = true;
            });

            checkExerciseCompletion();
        } else {
            this.classList.add('incorrect');
            enhancedSpeakLevel2('حاول مرة أخرى', 'ar', 'try_again.mp3');
            recordAnswer(false);
        }
    }

    function handleQuestionOptionClick(e) {
        const isCorrect = this.dataset.correct === 'true';

        if (isCorrect) {
            this.classList.add('correct');
            enhancedSpeakLevel2('ممتاز!', 'ar', 'excellent.mp3');
            celebrateCorrectAnswer();

            // تعطيل جميع الخيارات في هذا السؤال
            const questionDiv = this.closest('.question-item');
            questionDiv.querySelectorAll('.question-option').forEach(option => {
                option.disabled = true;
            });

            checkExerciseCompletion();
        } else {
            this.classList.add('incorrect');
            enhancedSpeakLevel2('حاول مرة أخرى', 'ar', 'try_again.mp3');
            recordAnswer(false);
        }
    }

    function handleCanOptionClick(e) {
        const isCorrect = this.dataset.correct === 'true';

        if (isCorrect) {
            this.classList.add('correct');
            enhancedSpeakLevel2('ممتاز!', 'ar', 'excellent.mp3');
            celebrateCorrectAnswer();

            // تعطيل جميع الخيارات في هذا السؤال
            const canDiv = this.closest('.can-item');
            canDiv.querySelectorAll('.can-option').forEach(option => {
                option.disabled = true;
            });

            checkExerciseCompletion();
        } else {
            this.classList.add('incorrect');
            enhancedSpeakLevel2('حاول مرة أخرى', 'ar', 'try_again.mp3');
            recordAnswer(false);
        }
    }

    function checkExerciseCompletion() {
        const currentExerciseType = exercises[currentExercise];
        let isCompleted = false;

        switch(currentExerciseType) {
            case 'pronouns':
                isCompleted = document.querySelectorAll('.sentence-slot.correct').length >= pronounsData.length;
                break;
            case 'verbs':
                isCompleted = document.querySelectorAll('.verb-question .correct').length >= verbsData.length;
                break;
            case 'questions':
                isCompleted = document.querySelectorAll('.question-item .correct').length >= questionsData.length;
                break;
            case 'can-statements':
                isCompleted = document.querySelectorAll('.can-item .correct').length >= canStatementsData.length;
                break;
        }

        if (isCompleted) {
            setTimeout(() => {
                document.getElementById('next-exercise-btn').style.display = 'block';
                enhancedSpeakLevel2('ممتاز! أكملت التمرين!', 'ar', 'excellent.mp3');
            }, 1000);
        }
    }

    function nextExercise() {
        currentExercise++;

        if (currentExercise >= totalExercises) {
            completeGrammarGame();
        } else {
            showGrammarGame();
        }
    }

    function completeGrammarGame() {
        enhancedSpeakLevel2('ممتاز! أكملت جميع تمارين القواعد!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            // انتهاء المستوى الثاني
            showResultsScreen();
        }, 3000);
    }

    // متغيرات مساعدة
    let selectedPronoun = null;

    showGrammarGame();
}

// تهيئة المرحلة الثانية
function initializeLevel2() {
    console.log('🍍 تهيئة المرحلة الثانية - الصف الثاني الابتدائي');
    checkLevel2Resources();
}

// تهيئة المرحلة الثانية
function initializeLevel2() {
    console.log('🍍 تهيئة المرحلة الثانية - الصف الثاني الابتدائي');
    checkLevel2Resources();
}
                        <p class="arabic-name">${clothingData.arabic}</p>
                        <p class="clothing-type">Type: ${clothingData.type}</p>
                        <p class="clothing-color">Color: ${clothingData.color}</p>
                        <button class="speak-again-btn" onclick="enhancedSpeakLevel2('${clothingData.word}', 'en', '${clothingData.word.toLowerCase()}_en.mp3')">
                            🔊 نطق مرة أخرى
                        </button>
                    </div>
                </div>
            `;
        }
    }

    function updateDressingProgress() {
        const dressedCount = document.getElementById('dressed-count');
        const progressFill = document.getElementById('clothes-progress-fill');

        if (dressedCount) {
            dressedCount.textContent = dressedItems;
        }

        if (progressFill) {
            const percentage = (dressedItems / totalItems) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeClothesGame() {
        enhancedSpeakLevel2('رائع! ألبست الشخصية جميع الملابس!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showAdvancedFoodGame();
}

    function showAdvancedFoodGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-food-game">
                <h2>🍊 لعبة الطعام المتقدمة</h2>
                <p class="game-instruction">اطبخ الوجبات باستخدام المكونات الصحيحة</p>
                <p class="game-instruction-en">Cook meals using the correct ingredients</p>

                <div class="cooking-container">
                    <div class="ingredients-area">
                        <h3>المكونات</h3>
                        <div class="ingredients-grid">
                            ${advancedFoodData.map(food => `
                                <div class="ingredient-item"
                                     data-ingredient="${food.word}"
                                     data-category="${food.category}">
                                    <div class="ingredient-icon">${food.emoji}</div>
                                    <div class="ingredient-name">${food.word}</div>
                                    <div class="ingredient-arabic">${food.arabic}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="cooking-station">
                        <h3>محطة الطبخ</h3>
                        <div class="current-meal" id="current-meal">
                            <div class="meal-info">
                                <div class="meal-icon">${meals[currentMeal].emoji}</div>
                                <div class="meal-details">
                                    <h4>${meals[currentMeal].name}</h4>
                                    <p>${meals[currentMeal].arabic}</p>
                                    <p class="needed-ingredients">
                                        المطلوب: ${meals[currentMeal].ingredients.join(', ')}
                                    </p>
                                </div>
                            </div>
                            <div class="cooking-pot">
                                <div class="pot-contents" id="pot-contents">
                                    <div class="pot-placeholder">أضف المكونات هنا</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="food-categories">
                    <h3>تصنيف الطعام</h3>
                    <div class="categories-display" id="categories-display">
                        <div class="category-info">انقر على طعام لرؤية تصنيفه</div>
                    </div>
                </div>

                <div class="cooking-progress">
                    <div class="progress-text">الوجبات المطبوخة: <span id="cooked-count">0</span> من ${totalMeals}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="food-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedFoodEvents();
    }

    function setupAdvancedFoodEvents() {
        const ingredientItems = document.querySelectorAll('.ingredient-item');
        const cookingPot = document.getElementById('pot-contents');

        ingredientItems.forEach(item => {
            item.addEventListener('click', handleIngredientClick);
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleIngredientClick.call(this, e);
            });
        });

        cookingPot.addEventListener('click', handlePotClick);
        cookingPot.addEventListener('touchend', function(e) {
            e.preventDefault();
            e.stopPropagation();
            handlePotClick.call(this, e);
        });
    }

    function handleIngredientClick(e) {
        e.preventDefault();

        // إلغاء تحديد المكون السابق
        if (selectedIngredient) {
            selectedIngredient.classList.remove('selected');
        }

        // تحديد المكون الجديد
        selectedIngredient = this;
        selectedIngredient.classList.add('selected');

        // نطق اسم المكون بالإنجليزية
        const ingredientName = selectedIngredient.dataset.ingredient;
        enhancedSpeakLevel2(ingredientName, 'en', `${ingredientName.toLowerCase()}_en.mp3`);

        // عرض تصنيف الطعام
        showFoodCategory(ingredientName);

        // تأثير بصري
        selectedIngredient.style.transform = 'scale(1.1)';
        setTimeout(() => {
            if (selectedIngredient) {
                selectedIngredient.style.transform = 'scale(1)';
            }
        }, 200);
    }

    function handlePotClick(e) {
        e.preventDefault();

        if (!selectedIngredient) {
            enhancedSpeakLevel2('اختر مكوناً أولاً', 'ar', 'try_again.mp3');

            // تأثير تنبيه للمكونات
            const ingredientsArea = document.querySelector('.ingredients-area');
            if (ingredientsArea) {
                ingredientsArea.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    ingredientsArea.style.animation = '';
                }, 600);
            }
            return;
        }

        addIngredientToPot(selectedIngredient);
    }

    function addIngredientToPot(ingredientElement) {
        const ingredientName = ingredientElement.dataset.ingredient;
        const currentMealData = meals[currentMeal];
        const potContents = document.getElementById('pot-contents');

        // التحقق من صحة المكون
        if (currentMealData.ingredients.includes(ingredientName)) {
            // مكون صحيح
            handleCorrectIngredient(ingredientElement, potContents);
        } else {
            // مكون خاطئ
            handleIncorrectIngredient(ingredientElement);
        }
    }

    function handleCorrectIngredient(ingredientElement, potContents) {
        const ingredientName = ingredientElement.dataset.ingredient;
        const ingredientEmoji = ingredientElement.querySelector('.ingredient-icon').textContent;

        // إضافة المكون للوعاء
        if (potContents.querySelector('.pot-placeholder')) {
            potContents.innerHTML = '';
        }

        const ingredientInPot = document.createElement('div');
        ingredientInPot.className = 'pot-ingredient';
        ingredientInPot.innerHTML = `
            <span class="pot-emoji">${ingredientEmoji}</span>
            <span class="pot-name">${ingredientName}</span>
        `;
        potContents.appendChild(ingredientInPot);

        // إخفاء المكون من المكونات
        ingredientElement.style.display = 'none';

        // إلغاء التحديد
        selectedIngredient = null;

        // تأثيرات بصرية وصوتية
        potContents.classList.add('ingredient-added');
        enhancedSpeakLevel2('مكون صحيح!', 'ar', 'correct.mp3');
        recordAnswer(true);

        setTimeout(() => {
            potContents.classList.remove('ingredient-added');
        }, 1000);

        // التحقق من اكتمال الوجبة
        const addedIngredients = potContents.querySelectorAll('.pot-ingredient').length;
        if (addedIngredients >= meals[currentMeal].ingredients.length) {
            setTimeout(() => {
                completeMeal();
            }, 1000);
        }
    }

    function handleIncorrectIngredient(ingredientElement) {
        // تأثير اهتزاز للمكون
        ingredientElement.classList.add('shake-animation');
        enhancedSpeakLevel2('هذا المكون لا يناسب هذه الوجبة', 'ar', 'try_again.mp3');
        recordAnswer(false);

        setTimeout(() => {
            ingredientElement.classList.remove('shake-animation');
            // إلغاء التحديد بعد المحاولة الخاطئة
            if (selectedIngredient) {
                selectedIngredient.classList.remove('selected');
                selectedIngredient = null;
            }
        }, 600);
    }

    function showFoodCategory(ingredientName) {
        const categoriesDisplay = document.getElementById('categories-display');
        const foodData = advancedFoodData.find(f => f.word === ingredientName);

        if (foodData) {
            categoriesDisplay.innerHTML = `
                <div class="category-info">
                    <div class="food-icon">${foodData.emoji}</div>
                    <div class="food-details">
                        <h4>${foodData.word}</h4>
                        <p class="arabic-name">${foodData.arabic}</p>
                        <p class="food-category">Category: ${foodData.category}</p>
                        <p class="food-taste">Taste: ${foodData.taste}</p>
                    </div>
                </div>
            `;
        }
    }

    function completeMeal() {
        const currentMealData = meals[currentMeal];

        // تأثيرات الاحتفال
        enhancedSpeakLevel2(`ممتاز! طبخت ${currentMealData.arabic}!`, 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        // تحديث التقدم
        cookedMeals++;
        updateCookingProgress();

        // الانتقال للوجبة التالية أو إنهاء اللعبة
        if (currentMeal < totalMeals - 1) {
            setTimeout(() => {
                nextMeal();
            }, 2000);
        } else {
            setTimeout(() => {
                completeAdvancedFoodGame();
            }, 2000);
        }
    }

    function nextMeal() {
        currentMeal++;

        // إعادة عرض المكونات المخفية
        const hiddenIngredients = document.querySelectorAll('.ingredient-item[style*="display: none"]');
        hiddenIngredients.forEach(ingredient => {
            ingredient.style.display = 'block';
        });

        // تحديث معلومات الوجبة الحالية
        const currentMealElement = document.getElementById('current-meal');
        const newMealData = meals[currentMeal];

        currentMealElement.querySelector('.meal-icon').textContent = newMealData.emoji;
        currentMealElement.querySelector('h4').textContent = newMealData.name;
        currentMealElement.querySelector('p').textContent = newMealData.arabic;
        currentMealElement.querySelector('.needed-ingredients').textContent =
            `المطلوب: ${newMealData.ingredients.join(', ')}`;

        // إعادة تعيين الوعاء
        const potContents = document.getElementById('pot-contents');
        potContents.innerHTML = '<div class="pot-placeholder">أضف المكونات هنا</div>';

        enhancedSpeakLevel2(`الآن اطبخ ${newMealData.arabic}`, 'ar', 'next_meal.mp3');
    }

    function updateCookingProgress() {
        const cookedCount = document.getElementById('cooked-count');
        const progressFill = document.getElementById('food-progress-fill');

        if (cookedCount) {
            cookedCount.textContent = cookedMeals;
        }

        if (progressFill) {
            const percentage = (cookedMeals / totalMeals) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeAdvancedFoodGame() {
        enhancedSpeakLevel2('رائع! طبخت جميع الوجبات!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showAdvancedFoodGame();
}

// لعبة الهوايات والأنشطة
function loadActivitiesGame() {
    let selectedActivity = null;
    let completedActivities = 0;
    const totalActivities = activitiesData.length;
    const shuffledActivities = shuffleArray([...activitiesData]);

    function showActivitiesGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="activities-game">
                <h2>🎨 لعبة الهوايات والأنشطة</h2>
                <p class="game-instruction">انقر على النشاط لتعلم جملة "أحب أن..."</p>
                <p class="game-instruction-en">Click on activities to learn "I like to..." sentences</p>

                <div class="activities-container">
                    <div class="activities-grid">
                        ${shuffledActivities.map((activity, index) => `
                            <div class="activity-card"
                                 data-activity="${activity.word}"
                                 data-action="${activity.action}"
                                 data-sentence="${activity.sentence}"
                                 data-index="${index}">
                                <div class="activity-animation" id="activity-${index}">
                                    <div class="activity-icon">${activity.emoji}</div>
                                    <div class="activity-character">👤</div>
                                </div>
                                <div class="activity-info">
                                    <h3>${activity.word}</h3>
                                    <p class="activity-arabic">${activity.arabic}</p>
                                    <p class="activity-sentence">${activity.sentence}</p>
                                </div>
                                <div class="activity-status" id="status-${index}">
                                    <span class="status-icon">⭐</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="sentence-builder">
                    <h3>بناء الجمل</h3>
                    <div class="sentence-practice" id="sentence-practice">
                        <div class="sentence-template">
                            <span class="sentence-part">I like to</span>
                            <span class="sentence-blank" id="sentence-blank">_____</span>
                        </div>
                        <div class="sentence-options" id="sentence-options">
                            <p>انقر على نشاط لإكمال الجملة</p>
                        </div>
                    </div>
                </div>

                <div class="activities-progress">
                    <div class="progress-text">الأنشطة المكتملة: <span id="activities-count">0</span> من ${totalActivities}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="activities-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupActivitiesEvents();
        startSentencePractice();
    }

    function setupActivitiesEvents() {
        const activityCards = document.querySelectorAll('.activity-card');

        activityCards.forEach(card => {
            card.addEventListener('click', handleActivityClick);
            card.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleActivityClick.call(this, e);
            });
        });
    }

    function handleActivityClick(e) {
        e.preventDefault();

        const card = this;
        const activityName = card.dataset.activity;
        const activityAction = card.dataset.action;
        const activitySentence = card.dataset.sentence;
        const activityIndex = card.dataset.index;

        // تحديد النشاط
        if (selectedActivity) {
            selectedActivity.classList.remove('selected');
        }
        selectedActivity = card;
        selectedActivity.classList.add('selected');

        // نطق اسم النشاط بالإنجليزية
        enhancedSpeakLevel2(activityName, 'en', `${activityName.toLowerCase()}_en.mp3`);

        // تشغيل انيميشن النشاط
        playActivityAnimation(activityIndex, activityAction);

        // تحديث بناء الجملة
        updateSentenceBuilder(activityName, activitySentence);

        // تأثير بصري
        card.style.transform = 'scale(1.05)';
        setTimeout(() => {
            card.style.transform = 'scale(1)';
        }, 300);

        // تحديث الحالة إذا لم يكن مكتملاً من قبل
        if (!card.classList.contains('completed')) {
            setTimeout(() => {
                completeActivity(card, activityIndex);
            }, 2000);
        }
    }

    function playActivityAnimation(index, action) {
        const animationElement = document.getElementById(`activity-${index}`);
        const character = animationElement.querySelector('.activity-character');

        // إزالة الانيميشن السابق
        character.classList.remove('playing', 'reading', 'drawing', 'singing', 'dancing', 'swimming');

        // إضافة الانيميشن الجديد
        setTimeout(() => {
            character.classList.add(action);
        }, 100);

        // تأثير الانيميشن
        animationElement.style.animation = 'activityPulse 2s ease-in-out';
        setTimeout(() => {
            animationElement.style.animation = '';
        }, 2000);
    }

    function updateSentenceBuilder(activityName, fullSentence) {
        const sentenceBlank = document.getElementById('sentence-blank');
        const sentenceOptions = document.getElementById('sentence-options');

        // تحديث الفراغ
        sentenceBlank.textContent = activityName.toLowerCase();
        sentenceBlank.classList.add('filled');

        // نطق الجملة الكاملة
        setTimeout(() => {
            enhancedSpeakLevel2(fullSentence, 'en', `sentence_${activityName.toLowerCase()}_en.mp3`);
        }, 1000);

        // عرض خيارات إضافية
        sentenceOptions.innerHTML = `
            <div class="sentence-complete">
                <p class="complete-sentence">"${fullSentence}"</p>
                <button class="repeat-sentence-btn" onclick="enhancedSpeakLevel2('${fullSentence}', 'en', 'sentence_${activityName.toLowerCase()}_en.mp3')">
                    🔊 كرر الجملة
                </button>
            </div>
        `;
    }

    function completeActivity(card, index) {
        card.classList.add('completed');

        // تحديث أيقونة الحالة
        const statusElement = document.getElementById(`status-${index}`);
        statusElement.innerHTML = '<span class="status-icon completed">✅</span>';

        // تحديث التقدم
        completedActivities++;
        updateActivitiesProgress();

        // تأثيرات الاحتفال
        card.classList.add('activity-completed');
        enhancedSpeakLevel2('ممتاز! تعلمت نشاطاً جديداً!', 'ar', 'excellent.mp3');

        setTimeout(() => {
            card.classList.remove('activity-completed');
        }, 1000);

        // التحقق من اكتمال اللعبة
        if (completedActivities >= totalActivities) {
            setTimeout(() => {
                completeActivitiesGame();
            }, 1500);
        } else {
            // بدء تمرين جديد
            setTimeout(() => {
                startSentencePractice();
            }, 3000);
        }
    }

    function startSentencePractice() {
        if (completedActivities < totalActivities) {
            const sentenceBlank = document.getElementById('sentence-blank');
            const sentenceOptions = document.getElementById('sentence-options');

            sentenceBlank.textContent = '_____';
            sentenceBlank.classList.remove('filled');

            sentenceOptions.innerHTML = '<p>انقر على نشاط لإكمال الجملة</p>';
        }
    }

    function updateActivitiesProgress() {
        const activitiesCount = document.getElementById('activities-count');
        const progressFill = document.getElementById('activities-progress-fill');

        if (activitiesCount) {
            activitiesCount.textContent = completedActivities;
        }

        if (progressFill) {
            const percentage = (completedActivities / totalActivities) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeActivitiesGame() {
        enhancedSpeakLevel2('رائع! تعلمت جميع الأنشطة والهوايات!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showActivitiesGame();
}

// لعبة الحيوانات المتقدمة
function loadAdvancedAnimalsGame() {
    let selectedAnimal = null;
    let fedAnimals = 0;
    const totalAnimals = advancedAnimalsData.length;
    const shuffledAnimals = shuffleArray([...advancedAnimalsData]);

    function showAdvancedAnimalsGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="advanced-animals-game">
                <h2>🐄 لعبة الحيوانات المتقدمة</h2>
                <p class="game-instruction">انقر على الحيوان لسماع صوته وإطعامه</p>
                <p class="game-instruction-en">Click on animals to hear their sounds and feed them</p>

                <div class="farm-container">
                    <div class="farm-background">
                        <div class="farm-animals">
                            ${shuffledAnimals.map((animal, index) => `
                                <div class="farm-animal"
                                     data-animal="${animal.word}"
                                     data-sound="${animal.sound}"
                                     data-habitat="${animal.habitat}"
                                     data-index="${index}">
                                    <div class="animal-container">
                                        <div class="animal-icon">${animal.emoji}</div>
                                        <div class="animal-name">${animal.word}</div>
                                        <div class="animal-arabic">${animal.arabic}</div>
                                        <div class="animal-sound">"${animal.sound}"</div>
                                    </div>
                                    <div class="animal-status" id="animal-status-${index}">
                                        <div class="hunger-indicator">🍽️</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="farm-controls">
                        <h3>رعاية الحيوانات</h3>
                        <div class="care-options">
                            <button class="care-btn feed-btn" id="feed-btn" onclick="feedSelectedAnimal()">
                                🌾 إطعام الحيوان
                            </button>
                            <button class="care-btn sound-btn" id="sound-btn" onclick="playAnimalSound()">
                                🔊 سماع الصوت
                            </button>
                            <button class="care-btn info-btn" id="info-btn" onclick="showAnimalInfo()">
                                ℹ️ معلومات الحيوان
                            </button>
                        </div>
                    </div>
                </div>

                <div class="animal-learning">
                    <h3>تعلم عن الحيوانات</h3>
                    <div class="animal-info-display" id="animal-info-display">
                        <div class="info-placeholder">
                            <div class="placeholder-icon">🐾</div>
                            <div class="placeholder-text">انقر على حيوان لتعلم المزيد عنه</div>
                        </div>
                    </div>
                </div>

                <div class="farm-progress">
                    <div class="progress-text">الحيوانات المطعمة: <span id="fed-count">0</span> من ${totalAnimals}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="animals-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupAdvancedAnimalsEvents();
    }

    function setupAdvancedAnimalsEvents() {
        const farmAnimals = document.querySelectorAll('.farm-animal');

        farmAnimals.forEach(animal => {
            animal.addEventListener('click', handleAnimalClick);
            animal.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                handleAnimalClick.call(this, e);
            });
        });
    }

    function handleAnimalClick(e) {
        e.preventDefault();

        const animal = this;
        const animalName = animal.dataset.animal;
        const animalSound = animal.dataset.sound;
        const animalIndex = animal.dataset.index;

        // تحديد الحيوان
        if (selectedAnimal) {
            selectedAnimal.classList.remove('selected');
        }
        selectedAnimal = animal;
        selectedAnimal.classList.add('selected');

        // نطق اسم الحيوان بالإنجليزية
        enhancedSpeakLevel2(animalName, 'en', `${animalName.toLowerCase()}_en.mp3`);

        // عرض معلومات الحيوان
        showAnimalDetails(animalName);

        // تشغيل انيميشن الحيوان
        playAnimalAnimation(animalIndex);

        // تأثير بصري
        animal.style.transform = 'scale(1.05)';
        setTimeout(() => {
            animal.style.transform = 'scale(1)';
        }, 300);

        // تشغيل صوت الحيوان بعد قليل
        setTimeout(() => {
            playAnimalSoundEffect(animalSound);
        }, 1000);
    }

    function playAnimalAnimation(index) {
        const animalElement = selectedAnimal.querySelector('.animal-container');

        // تأثير الحركة
        animalElement.style.animation = 'animalBounce 1s ease-in-out';
        setTimeout(() => {
            animalElement.style.animation = '';
        }, 1000);
    }

    function playAnimalSoundEffect(sound) {
        // نطق صوت الحيوان
        enhancedSpeakLevel2(sound, 'en', `${sound.toLowerCase()}_sound.mp3`);

        // تأثير بصري لصوت الحيوان
        if (selectedAnimal) {
            const soundElement = selectedAnimal.querySelector('.animal-sound');
            soundElement.style.animation = 'soundPulse 1s ease-in-out';
            setTimeout(() => {
                soundElement.style.animation = '';
            }, 1000);
        }
    }

    function showAnimalDetails(animalName) {
        const animalInfoDisplay = document.getElementById('animal-info-display');
        const animalData = advancedAnimalsData.find(a => a.word === animalName);

        if (animalData) {
            animalInfoDisplay.innerHTML = `
                <div class="animal-details">
                    <div class="detail-icon">${animalData.emoji}</div>
                    <div class="detail-info">
                        <h4>${animalData.word}</h4>
                        <p class="arabic-name">${animalData.arabic}</p>
                        <p class="animal-sound-info">Sound: "${animalData.sound}"</p>
                        <p class="animal-habitat">Habitat: ${animalData.habitat}</p>
                        <div class="animal-actions">
                            <button class="action-btn" onclick="enhancedSpeakLevel2('${animalData.word}', 'en', '${animalData.word.toLowerCase()}_en.mp3')">
                                🔊 نطق الاسم
                            </button>
                            <button class="action-btn" onclick="playAnimalSoundEffect('${animalData.sound}')">
                                🎵 صوت الحيوان
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // وظائف الأزرار العامة
    window.feedSelectedAnimal = function() {
        if (!selectedAnimal) {
            enhancedSpeakLevel2('اختر حيواناً أولاً', 'ar', 'try_again.mp3');
            return;
        }

        const animalIndex = selectedAnimal.dataset.index;
        feedAnimal(selectedAnimal, animalIndex);
    };

    window.playAnimalSound = function() {
        if (!selectedAnimal) {
            enhancedSpeakLevel2('اختر حيواناً أولاً', 'ar', 'try_again.mp3');
            return;
        }

        const animalSound = selectedAnimal.dataset.sound;
        playAnimalSoundEffect(animalSound);
    };

    window.showAnimalInfo = function() {
        if (!selectedAnimal) {
            enhancedSpeakLevel2('اختر حيواناً أولاً', 'ar', 'try_again.mp3');
            return;
        }

        const animalName = selectedAnimal.dataset.animal;
        showAnimalDetails(animalName);
    };

    function feedAnimal(animalElement, index) {
        // التحقق من إطعام الحيوان من قبل
        if (animalElement.classList.contains('fed')) {
            enhancedSpeakLevel2('هذا الحيوان مُطعم بالفعل', 'ar', 'already_fed.mp3');
            return;
        }

        // إطعام الحيوان
        animalElement.classList.add('fed');

        // تحديث حالة الحيوان
        const statusElement = document.getElementById(`animal-status-${index}`);
        statusElement.innerHTML = '<div class="fed-indicator">✅ مُطعم</div>';

        // تحديث التقدم
        fedAnimals++;
        updateAnimalsProgress();

        // تأثيرات بصرية وصوتية
        animalElement.classList.add('feeding-animation');
        enhancedSpeakLevel2('ممتاز! أطعمت الحيوان!', 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        setTimeout(() => {
            animalElement.classList.remove('feeding-animation');
        }, 2000);

        // التحقق من اكتمال اللعبة
        if (fedAnimals >= totalAnimals) {
            setTimeout(() => {
                completeAdvancedAnimalsGame();
            }, 1500);
        }
    }

    function updateAnimalsProgress() {
        const fedCount = document.getElementById('fed-count');
        const progressFill = document.getElementById('animals-progress-fill');

        if (fedCount) {
            fedCount.textContent = fedAnimals;
        }

        if (progressFill) {
            const percentage = (fedAnimals / totalAnimals) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeAdvancedAnimalsGame() {
        enhancedSpeakLevel2('رائع! أطعمت جميع حيوانات المزرعة!', 'ar', 'excellent.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            nextGame();
        }, 3000);
    }

    showAdvancedAnimalsGame();
}

// لعبة القواعد والجمل
function loadGrammarGame() {
    let currentExercise = 0;
    let completedExercises = 0;
    const totalExercises = 4; // ضمائر، أفعال، أسئلة، can statements
    const exercises = [
        { type: 'pronouns', title: 'الضمائر الشخصية', titleEn: 'Personal Pronouns' },
        { type: 'verbs', title: 'الأفعال والتصريف', titleEn: 'Verbs and Conjugation' },
        { type: 'questions', title: 'أسئلة Wh', titleEn: 'Wh Questions' },
        { type: 'can', title: 'استخدام Can', titleEn: 'Using Can' }
    ];

    function showGrammarGame() {
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="grammar-game">
                <h2>💬 لعبة القواعد والجمل</h2>
                <p class="game-instruction">تعلم قواعد اللغة الإنجليزية من خلال التمارين التفاعلية</p>
                <p class="game-instruction-en">Learn English grammar through interactive exercises</p>

                <div class="grammar-container">
                    <div class="exercise-selector">
                        <h3>اختر التمرين</h3>
                        <div class="exercise-buttons">
                            ${exercises.map((exercise, index) => `
                                <button class="exercise-btn ${index === currentExercise ? 'active' : ''}"
                                        data-exercise="${index}"
                                        onclick="switchExercise(${index})">
                                    <span class="exercise-title">${exercise.title}</span>
                                    <span class="exercise-title-en">${exercise.titleEn}</span>
                                    <span class="exercise-status" id="status-${index}">⭐</span>
                                </button>
                            `).join('')}
                        </div>
                    </div>

                    <div class="exercise-area">
                        <div class="exercise-content" id="exercise-content">
                            <!-- سيتم تحديث المحتوى ديناميكياً -->
                        </div>
                    </div>
                </div>

                <div class="grammar-progress">
                    <div class="progress-text">التمارين المكتملة: <span id="grammar-count">0</span> من ${totalExercises}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="grammar-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        setupGrammarEvents();
        loadCurrentExercise();
    }

    function setupGrammarEvents() {
        // سيتم إعداد الأحداث ديناميكياً لكل تمرين
    }

    window.switchExercise = function(exerciseIndex) {
        currentExercise = exerciseIndex;

        // تحديث الأزرار
        document.querySelectorAll('.exercise-btn').forEach((btn, index) => {
            btn.classList.toggle('active', index === exerciseIndex);
        });

        loadCurrentExercise();
    };

    function loadCurrentExercise() {
        const exerciseType = exercises[currentExercise].type;

        switch(exerciseType) {
            case 'pronouns':
                loadPronounsExercise();
                break;
            case 'verbs':
                loadVerbsExercise();
                break;
            case 'questions':
                loadQuestionsExercise();
                break;
            case 'can':
                loadCanExercise();
                break;
        }
    }

    function loadPronounsExercise() {
        const exerciseContent = document.getElementById('exercise-content');
        exerciseContent.innerHTML = `
            <div class="pronouns-exercise">
                <h3>تعلم الضمائر الشخصية</h3>
                <div class="pronouns-grid">
                    ${grammarData.pronouns.map((pronoun, index) => `
                        <div class="pronoun-card" data-pronoun="${pronoun.word}" onclick="learnPronoun('${pronoun.word}', '${pronoun.arabic}', '${pronoun.example}')">
                            <div class="pronoun-icon">${pronoun.emoji}</div>
                            <div class="pronoun-word">${pronoun.word}</div>
                            <div class="pronoun-arabic">${pronoun.arabic}</div>
                            <div class="pronoun-example">${pronoun.example}</div>
                        </div>
                    `).join('')}
                </div>
                <div class="exercise-completion">
                    <button class="complete-btn" onclick="completePronounsExercise()">
                        ✅ أكملت تعلم الضمائر
                    </button>
                </div>
            </div>
        `;
    }

    function loadVerbsExercise() {
        const exerciseContent = document.getElementById('exercise-content');
        exerciseContent.innerHTML = `
            <div class="verbs-exercise">
                <h3>تعلم الأفعال والتصريف</h3>
                <div class="verb-practice">
                    ${grammarData.verbs.map((verb, index) => `
                        <div class="verb-card" data-verb="${verb.word}">
                            <h4>${verb.word} (${verb.arabic})</h4>
                            <div class="verb-forms">
                                <div class="form-item">
                                    <span class="subject">I</span>
                                    <span class="verb-form">${verb.forms.I}</span>
                                    <button onclick="speakSentence('I ${verb.forms.I}')">🔊</button>
                                </div>
                                <div class="form-item">
                                    <span class="subject">He</span>
                                    <span class="verb-form">${verb.forms.He}</span>
                                    <button onclick="speakSentence('He ${verb.forms.He}')">🔊</button>
                                </div>
                                <div class="form-item">
                                    <span class="subject">She</span>
                                    <span class="verb-form">${verb.forms.She}</span>
                                    <button onclick="speakSentence('She ${verb.forms.She}')">🔊</button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="exercise-completion">
                    <button class="complete-btn" onclick="completeVerbsExercise()">
                        ✅ أكملت تعلم الأفعال
                    </button>
                </div>
            </div>
        `;
    }

    function loadQuestionsExercise() {
        const exerciseContent = document.getElementById('exercise-content');
        exerciseContent.innerHTML = `
            <div class="questions-exercise">
                <h3>تعلم أسئلة Wh</h3>
                <div class="questions-grid">
                    ${grammarData.questions.map((question, index) => `
                        <div class="question-card" data-question="${question.word}" onclick="learnQuestion('${question.word}', '${question.arabic}', '${question.example}')">
                            <div class="question-icon">${question.emoji}</div>
                            <div class="question-word">${question.word}</div>
                            <div class="question-arabic">${question.arabic}</div>
                            <div class="question-example">${question.example}</div>
                        </div>
                    `).join('')}
                </div>
                <div class="exercise-completion">
                    <button class="complete-btn" onclick="completeQuestionsExercise()">
                        ✅ أكملت تعلم الأسئلة
                    </button>
                </div>
            </div>
        `;
    }

    function loadCanExercise() {
        const exerciseContent = document.getElementById('exercise-content');
        exerciseContent.innerHTML = `
            <div class="can-exercise">
                <h3>تعلم استخدام Can</h3>
                <div class="can-statements">
                    ${grammarData.canStatements.map((statement, index) => `
                        <div class="can-card" data-statement="${statement.sentence}">
                            <div class="can-positive">
                                <span class="can-sentence">${statement.sentence}</span>
                                <button onclick="speakSentence('${statement.sentence}')">🔊</button>
                            </div>
                            <div class="can-negative">
                                <span class="can-sentence">${statement.negative}</span>
                                <button onclick="speakSentence('${statement.negative}')">🔊</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="exercise-completion">
                    <button class="complete-btn" onclick="completeCanExercise()">
                        ✅ أكملت تعلم Can
                    </button>
                </div>
            </div>
        `;
    }

    // وظائف التفاعل
    window.learnPronoun = function(pronoun, arabic, example) {
        enhancedSpeakLevel2(pronoun, 'en', `${pronoun.toLowerCase()}_en.mp3`);
        setTimeout(() => {
            enhancedSpeakLevel2(example, 'en', `example_${pronoun.toLowerCase()}_en.mp3`);
        }, 1000);
    };

    window.learnQuestion = function(question, arabic, example) {
        enhancedSpeakLevel2(question, 'en', `${question.toLowerCase()}_en.mp3`);
        setTimeout(() => {
            enhancedSpeakLevel2(example, 'en', `question_${question.toLowerCase()}_en.mp3`);
        }, 1000);
    };

    window.speakSentence = function(sentence) {
        enhancedSpeakLevel2(sentence, 'en', `sentence_${sentence.replace(/\s+/g, '_').toLowerCase()}_en.mp3`);
    };

    // وظائف إكمال التمارين
    window.completePronounsExercise = function() {
        completeExercise(0, 'ممتاز! تعلمت الضمائر الشخصية!');
    };

    window.completeVerbsExercise = function() {
        completeExercise(1, 'رائع! تعلمت تصريف الأفعال!');
    };

    window.completeQuestionsExercise = function() {
        completeExercise(2, 'ممتاز! تعلمت أسئلة Wh!');
    };

    window.completeCanExercise = function() {
        completeExercise(3, 'رائع! تعلمت استخدام Can!');
    };

    function completeExercise(exerciseIndex, message) {
        // تحديث حالة التمرين
        const statusElement = document.getElementById(`status-${exerciseIndex}`);
        statusElement.textContent = '✅';

        // تحديث التقدم
        if (exerciseIndex === currentExercise && !exercises[exerciseIndex].completed) {
            exercises[exerciseIndex].completed = true;
            completedExercises++;
            updateGrammarProgress();
        }

        // تأثيرات الاحتفال
        enhancedSpeakLevel2(message, 'ar', 'excellent.mp3');
        celebrateCorrectAnswer();

        // التحقق من اكتمال جميع التمارين
        if (completedExercises >= totalExercises) {
            setTimeout(() => {
                completeGrammarGame();
            }, 2000);
        } else {
            // الانتقال للتمرين التالي
            setTimeout(() => {
                if (currentExercise < totalExercises - 1) {
                    switchExercise(currentExercise + 1);
                }
            }, 2000);
        }
    }

    function updateGrammarProgress() {
        const grammarCount = document.getElementById('grammar-count');
        const progressFill = document.getElementById('grammar-progress-fill');

        if (grammarCount) {
            grammarCount.textContent = completedExercises;
        }

        if (progressFill) {
            const percentage = (completedExercises / totalExercises) * 100;
            progressFill.style.width = percentage + '%';
        }
    }

    function completeGrammarGame() {
        enhancedSpeakLevel2('مبروك! أكملت جميع تمارين القواعد!', 'ar', 'level_complete.mp3');
        celebrateGameCompletion();

        setTimeout(() => {
            // إكمال المرحلة الثانية
            completeLevelTwo();
        }, 3000);
    }

    function completeLevelTwo() {
        enhancedSpeakLevel2('تهانينا! أكملت المرحلة الثانية بنجاح!', 'ar', 'level2_complete.mp3');

        // عرض شاشة إكمال المرحلة
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="level-completion">
                <h1>🎉 تهانينا! 🎉</h1>
                <h2>أكملت المرحلة الثانية بنجاح!</h2>
                <p>تعلمت الكثير من المفردات والقواعد الجديدة</p>
                <div class="completion-stats">
                    <div class="stat-item">
                        <span class="stat-number">8</span>
                        <span class="stat-label">ألعاب مكتملة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">كلمة جديدة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4</span>
                        <span class="stat-label">قواعد نحوية</span>
                    </div>
                </div>
                <button class="return-btn" onclick="returnToLevels()">
                    🏠 العودة للمراحل
                </button>
            </div>
        `;
    }

    showGrammarGame();
}

// تهيئة المرحلة الثانية
function initializeLevel2() {
    console.log('🍍 تهيئة المرحلة الثانية - الصف الثاني الابتدائي');
    checkLevel2Resources();
}
